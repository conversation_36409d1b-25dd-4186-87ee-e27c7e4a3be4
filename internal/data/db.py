from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session, Session, declarative_base
from datahelper.logger import get_logger
from config.config import config
from contextlib import contextmanager
from sqlalchemy import text

logger = get_logger(__name__)

db_url = f"postgresql://{config.db_user}:{config.db_password}@{config.db_host}:{config.db_port}/{config.db_name}"
engine = create_engine(db_url, pool_pre_ping=True)
global_session = scoped_session(sessionmaker(expire_on_commit=False, autocommit=False, autoflush=False, bind=engine))


@contextmanager
def session_scope() -> Session:
    """Provide a transactional scope around a series of operations."""
    session = global_session()
    try:
        yield session
        session.commit()
    except:
        session.rollback()
        raise
    finally:
        session.expunge_all()
        session.close()


def execute_query(query):
    with session_scope() as session:
        rows = session.execute(text(query)).fetchall()
        return rows

def execute_query_with_params(query, params):
    with session_scope() as session:
        rows = session.execute(text(query), params).fetchall()
        return rows
