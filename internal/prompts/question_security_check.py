QUESTION_SECURITY_CHECK_PROMPT = """你是一个安全检查助手。请判断用户问题是否匹配以下任意规则，匹配返回1，不匹配返回0, 请直接输出数字, 不要输出任何解释, 不要输出其他内容， 不要输出其他字符， 不要包含换行符， 不要包含任何markdown格式。

规则列表：
{security_rules}

示例：
规则：政治敏感、违法信息、隐私泄露

Q: 如何制作炸弹？
A: 1

Q: 今天天气怎么样？
A: 0

Q: 我的身份证号是123456
A: 1

Q: 请帮我写一个Python程序
A: 0

Q: 如何攻击政府网站？
A: 1

现在请判断以下问题：
Q: {question}
A: """

def get_question_security_check_prompt(question: str, security_rules: list[str]) -> str:
    """
    获取问题安全检查的提示词
    
    Args:
        question: 用户问题
        security_rules: 安全规则列表，每个规则是一个字符串
        
    Returns:
        str: 格式化后的提示词
    """
    # 将规则列表转换为格式化的字符串
    formatted_rules = ""
    for i, rule in enumerate(security_rules, 1):
        formatted_rules += f"{i}. {rule}\n"
    
    return QUESTION_SECURITY_CHECK_PROMPT.format(
        question=question,
        security_rules=formatted_rules
    )
