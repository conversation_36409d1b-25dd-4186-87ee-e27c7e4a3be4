import json
from typing import List

import grpc
import httpx
from dataembedding.rerank.rerank_pb2 import SentencePair
from datahelper.logger import get_logger
from openai import OpenAI, AsyncOpenAI
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam

from aiapi.agent.agent_pb2 import CallAgentReply
from aiapi.search.search_pb2 import QASearchReply, QAReplyType, QAStatus
from config.config import config
from internal.data.grpc import embedding_client
from internal.prompts.atomic_question import can_answer_question_prompt
from internal.prompts.contract_review import contract_review_json_schema
from internal.utils.convert import to_chunk_payloads, to_chunk_payloads_for_agent

logger = get_logger(__name__)

processes = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]

models = {
    1: {
        "name": "hunyuan-turbo",
        "url": "https://api.hunyuan.cloud.tencent.com/v1"
    }
}


def check_is_json_array(content):
    content = content.replace("\n", "")
    if not content.startswith("[") or not content.endswith("]"):
        raise ValueError(f"返回结果不是 json 数组: {content}")
    if content.count("'") % 2 != 0:
        raise ValueError(f"返回结果中存在奇数个单引号: {content}")
    if content.count("[") > 1 or content.count("]") > 1:
        raise ValueError(f"返回结果中存在多个数组: {content}")


def unique(result_keywords):
    result_keywords = list(set(result_keywords))
    return result_keywords


class LLM:
    def __init__(self):
        self.embedding_client = embedding_client
        self.client = OpenAI(
            base_url=f'http://{config.llm_host}:{config.llm_port}/v1',
            api_key=config.llm_api_key
        )
        self.aclient = AsyncOpenAI(
            base_url=f'http://{config.llm_host}:{config.llm_port}/v1',
            api_key=config.llm_api_key
        )
        self.external_client = OpenAI(
            base_url=config.external_llm_url,
            api_key=config.external_llm_api_key
        )
        self.aexternal_client = AsyncOpenAI(
            base_url=config.external_llm_url,
            api_key=config.external_llm_api_key
        )
        self.extract_format = "请从以下文字中提取关键词，必须以 json 字符串列表格式直接返回，返回结果内仅保留原始文字中的关键字, 不要包含多余的引号, 内容：'{}' "
        self.relation_reference_format = "现有一个问题: '{}', 和一组问答信息: '{}'," \
                                         " 我需要判断这个问题和这组问答信息之间是否存在关联关系, 如果存在关联关系请回答 1," \
                                         " 否则回答 0, 如果无法判断默认回答 1, 不要道歉"
        self.retrieve_on_demand_format = "现有一个问题: '{}', 请判断回答这个问题是否需要外部数据或特定文档来协助回答？如果需要请回答 1," \
                                         " 否则回答 0, 如果无法判断默认回答 1, 不要道歉, 不要以其他方式回答仅回答 0 或者 1 即可"
        self.multiple_round_retrieve_on_demand_format = """现有一个问题: '{}', 用以下三个反引号之间的文本内容是否可以回答该问题？
                                                           如果可以请回答 0, 否则回答 1, 如果无法判断默认回答 1, 不要道歉,
                                                            不要以其他方式回答仅回答 0 或者 1 即可。
                                                           内容: ```{}```
                                                        """
        self.abstract_format = """
                               你的任务是从给点一段文本内容进行内容提取, 只输出摘要, 不添加任何其他信息, 输出长度不超过 500 个字。
                               请从以下三个反引号之间的文本内容中提取'{}'相关的信息。
                               内容: ```{}``` /no_think
                               """

    def create_llm_client(self, url, api_key):
        # 通过参数支持代理设置
        return OpenAI(
            base_url=url,
            api_key=api_key,
            http_client=httpx.Client(
                proxy=config.llm_proxy,
            ) if config.llm_proxy else None
        )

    def create_llm_aclient(self, url, api_key):
        # 通过参数支持代理设置
        return AsyncOpenAI(
            base_url=url,
            api_key=api_key,
            http_client=httpx.AsyncClient(
                proxy=config.llm_proxy,
            ) if config.llm_proxy else None
        )
    
    def question_security_check(self, prompt):
        response = self.client.chat.completions.create(
            model=config.llm_model_name, temperature=0,
            messages=[
                ChatCompletionUserMessageParam(content=prompt + " /no_think", role="user")
            ],
            extra_body={"chat_template_kwargs": {"enable_thinking": False}},
            # extra_body={"enable_thinking": False},
        )
        return response.choices[0].message.content
    def contract_review(self, prompt, file_content):
        response = self.client.chat.completions.create(
            model=config.llm_model_name, temperature=0,
            messages=[
                ChatCompletionSystemMessageParam(
                    content=prompt,
                    role="system"
                ),
                ChatCompletionUserMessageParam(
                    content=file_content,
                    role="user"
                )
            ],
            extra_body={"guided_json": contract_review_json_schema,"chat_template_kwargs":{
                "enable_thinking": False
            }}, )
        content = response.choices[0].message.content
        return content

    def question_semantic_cache_check(self, prompt):
        response = self.client.chat.completions.create(
            model=config.llm_model_name, temperature=0,
            messages=[
                ChatCompletionUserMessageParam(
                    content=prompt,
                    role="user"
                )
            ],
            extra_body={"chat_template_kwargs": {"enable_thinking": False}}, )
        return response.choices[0].message.content
    
    def question_semantic_equivalence_decision_check(self, prompt):
        response = self.client.chat.completions.create(
            model=config.llm_model_name, temperature=0,
            messages=[
                ChatCompletionUserMessageParam(
                    content=prompt,
                    role="user"
                )
            ],
            extra_body={"chat_template_kwargs": {"enable_thinking": False}}, )
        return response.choices[0].message.content

    def extract_keywords(self, query) -> list[str]:
        response = self.client.chat.completions.create(model=config.llm_model_name,
                                                       temperature=0,
                                                       extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                                                       messages=[
                                                           {'role': 'user',
                                                            'content': self.extract_format.format(query) + ' /no_think'}
                                                       ])
        content = response.choices[0].message.content
        logger.info(f"原始问题: {query}, 分词结果: {content}")
        check_is_json_array(content)
        keywords = json.loads(json.dumps(eval(content)))

        result_keywords = []
        for keyword in keywords:
            result_keywords.append(keyword.strip("'"))

        return unique(result_keywords)

    def find_relation_question_indexs(self, question, histories) -> str:
        qa_group = ""
        for i, history in enumerate(histories):
            qa_group += "{}. 问题: {}, 答案: {}".format(i + 1, history[1], history[2] or '')

        prompt = '你是密数万象推理助手,你的任务是比对一个问题和一组QA（问答集）是否存在上下文的关联，这将用于查询多轮对话的上下文。' + \
                 '这里是要对比的问题：<question>{}</question>， 这里是问答集<qa_set>{}</qa_set> ' + \
                 '在判断是否存在上下文关联时，请考虑以下标准：' + \
                 '1. 问题中的关键概念是否在QA中的问题或答案部分有所提及。' + \
                 '2. 问题的主题是否与QA中的主题相关。' + \
                 '3. 问题是否基于QA中的回答而产生（如果存在多轮对话的情况）。' + \
                 '请根据以上标准判断问题与问答集是否存在上下文关联，如果存在关联，请返回关联的QA的索引，索引从1开始，多个索引之间用逗号(,)分隔, 若无关联，请返回0。最多返回5个索引。不需要返回任何其他信息。'
        prompt = prompt.format(question, qa_group)

        response = self.client.chat. \
            completions.create(model=config.llm_model_name,
                               temperature=0,
                               extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                               messages=[
                                   {
                                       'role': 'system',
                                       'content': prompt + ' /no_think'
                                   }
                               ])
        content = response.choices[0].message.content
        return content

    def relation_reference(self, query, histories) -> int:
        qa_group = ""
        for i, history in enumerate(histories):
            qa_group += "{}. 问题: {}, 答案: {}".format(i + 1, history.query, history.answer)

        response = self.client.chat. \
            completions.create(model=config.llm_model_name,
                               temperature=0,
                               extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                               messages=[
                                   {
                                       'role': 'system',
                                       'content': '你是密数万象推理助手,'
                                                  ' 用于推理一个问题和一组问答信息之间是否存在关联关系'
                                   },
                                   {
                                       'role': 'user',
                                       'content': self.relation_reference_format.format(query, qa_group)
                                   }
                               ])
        content = response.choices[0].message.content
        logger.info(f"原始问题: {query} \n 问答信息: {qa_group} \n 推理结果: {content}")

        if "'" in content or '"' in content:
            content.replace('"', '')
            content.replace("'", '')

        if len(content) > 1:
            return 0
        return int(content)

    def retrieve_on_demand(self, query, messages, multiple):
        if multiple:
            content = self.multiple_round_retrieve_on_demand_format.format(query, messages)
        else:
            content = self.retrieve_on_demand_format.format(query)
        print(len(content))
        response = self.client.chat. \
            completions.create(model=config.llm_model_name,
                               temperature=0,
                               extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                               messages=[
                                   {
                                       'role': 'system',
                                       'content': '你是密数万象推理助手,'
                                                  ' 用于推理回答一个问题是否需要额外检索其他信息来协助回答'
                                   },
                                   {
                                       'role': 'user',
                                       'content': content
                                   }
                               ])

        result = response.choices[0].message.content
        logger.info(f"原始问题: {query} \n 额外检索推理结果: {result}")

        if "'" in result or '"' in result:
            result.replace('"', '')
            result.replace("'", '')

        if len(result) > 1:
            return 1
        return int(result)

    async def abstract(self, queries, payload, index, payloads, context: grpc.aio.ServicerContext):
        process_index = 0
        process_end_count = 0
        content = payload.get("content")
        if len(content) > config.llm_max_token_per_round:
            content = content[:config.llm_max_token_per_round - 500]
        response = self.client.chat. \
            completions.create(model=config.llm_model_name,
                               temperature=0,
                               stream=True,
                               extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                               messages=[
                                   {
                                       'role': 'system',
                                       'content': '你是密数万象摘要助手,'
                                                  ' 用于一段文字按特定信息进行摘要'
                                   },
                                   {
                                       'role': 'user',
                                       'content': self.abstract_format.format("、".join(queries), content)
                                   }
                               ],
                               max_tokens=8192,
                               frequency_penalty=0,
                               presence_penalty=0
                               )
        result = ""

        if index == 0:
            # 第一个消息声明需要处理的所有文件
            first_reply = QASearchReply(
                content="",
                status=QAStatus.RUNNING,
                payloads=to_chunk_payloads(payloads),
                type=QAReplyType.ABSTRACT_PROCESS,
            )
            await context.write(first_reply)

        for i, chunk in enumerate(response):
            # logger.info(f"file_relation_id: {payload.get('fileRelationID')}, chunk index: {i}")
            choice = chunk.choices[0]
            result += choice.delta.content
            length = len(result)
            if length > 500:
                length = 500
            process = int(length / 500 * 100)

            if choice.finish_reason == "stop":
                await context.write(QASearchReply(
                    content="100",
                    status=QAStatus.STOPPED,
                    payloads=to_chunk_payloads([payload]),
                    type=QAReplyType.ABSTRACT_PROCESS,
                ))

            if process == 100:
                # 当摘要文本超长时, 进度条会到 100, 通过 process_end_count 参数来控制返回消息的频率
                process_end_count += 1
                if process_end_count < 50:
                    continue
                else:
                    process_end_count = 0

            if process == 100 or process == processes[process_index]:
                reply = QASearchReply(
                    content=str(process),
                    status=QAStatus.RUNNING,
                    payloads=to_chunk_payloads([payload]),
                    type=QAReplyType.ABSTRACT_PROCESS,
                )
                await context.write(reply)
                process_index += 1

        return result

    async def abstract_for_agent(self, queries, payload, index, payloads, context):
        process_index = 0
        process_end_count = 0
        content = payload.get("content", "")
        if len(content) > (config.llm_max_token_per_round / 1.5):
            content = content[:int(config.llm_max_token_per_round / 1.5) - 2000]
        response = self.client.chat.completions.create(model=config.llm_model_name,
                               temperature=0,
                               stream=True,
                               extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                               messages=[
                                   {
                                       'role': 'system',
                                       'content': '你是密数万象摘要助手,'
                                                  ' 用于一段文字按特定信息进行摘要'
                                   },
                                   {
                                       'role': 'user',
                                       'content': self.abstract_format.format("、".join(queries), content)
                                   }
                               ],
                               max_tokens=8192,
                               frequency_penalty=0,
                               presence_penalty=0
                               )
        result = ""

        if index == 0:
            # 第一个消息声明需要处理的所有文件
            first_reply = CallAgentReply(
                content="",
                status=QAStatus.RUNNING,
                payloads=to_chunk_payloads_for_agent(payloads),
                type=QAReplyType.ABSTRACT_PROCESS,
            )
            await context.write(first_reply)

        for i, chunk in enumerate(response):
            # logger.info(f"file_relation_id: {payload.get('fileRelationID')}, chunk index: {i}")
            choice = chunk.choices[0]
            if choice.delta.content is None:
                continue
            result += choice.delta.content
            length = len(result)
            if length > 500:
                length = 500
            process = int(length / 500 * 100)

            if choice.finish_reason == "stop":
                await context.write(CallAgentReply(
                    content="100",
                    status=QAStatus.STOPPED,
                    payloads=to_chunk_payloads_for_agent([payload]),
                    type=QAReplyType.ABSTRACT_PROCESS,
                ))

            if process == 100:
                # 当摘要文本超长时, 进度条会到 100, 通过 process_end_count 参数来控制返回消息的频率
                process_end_count += 1
                if process_end_count < 50:
                    continue
                else:
                    process_end_count = 0

            if process == 100 or process == processes[process_index]:
                reply = CallAgentReply(
                    content=str(process),
                    status=QAStatus.RUNNING,
                    payloads=to_chunk_payloads_for_agent([payload]),
                    type=QAReplyType.ABSTRACT_PROCESS,
                )
                await context.write(reply)
                process_index += 1

        return result

    def knowledge_rerank(self, query, knowledges: List[str], threshold=0.3, max_num=5):
        if query == "" or len(knowledges) == 0:
            return []

        pairs = []
        for knowledge in knowledges:
            pair = SentencePair(sentenceA=query, sentenceB=knowledge)
            pairs.append(pair)

        reply = self.embedding_client.rerank(pairs)
        indexed_scores = list(enumerate(reply.scores))
        sorted_indices = sorted(indexed_scores, key=lambda x: x[1], reverse=True)
        top_indices = [idx for idx, score in sorted_indices[:min(max_num, len(sorted_indices))] if score > threshold]
        return top_indices

    def can_answer_question(self, question: str, contexts: str, history_questions: list = None) -> dict:
        """判断上下文是否能回答问题
        """
        try:
            p = can_answer_question_prompt(
                question=question,
                contexts=contexts,
                history_questions=history_questions
            )
            response = self.client.chat.completions.create(
                model=config.llm_model_name,
                temperature=0,
                extra_body={"chat_template_kwargs": {"enable_thinking": False}},
                messages=[{
                    'role': 'user',
                    'content': p + " /no_think"
                }]
            )
            result = response.choices[0].message.content.strip()

            try:
                # 解析JSON格式的返回结果
                result_json = json.loads(result)

                # 确保所有必要字段都存在且类型正确
                useful_indexes = result_json.get("useful_indexes", [])
                if not isinstance(useful_indexes, list):
                    useful_indexes = []

                can_answer = result_json.get("can_answer", False)
                if not isinstance(can_answer, bool):
                    can_answer = str(can_answer).lower() == "true"

                new_question = result_json.get("new_question", "")
                if not isinstance(new_question, str):
                    new_question = str(new_question)

                return {
                    "can_ask": can_answer,
                    "new_question": new_question,
                    "useful_indexes": useful_indexes
                }

            except json.JSONDecodeError as e:
                logger.error(f"解析LLM返回的JSON失败: {str(e)}, 原始结果: {result}")
                return {
                    "can_ask": False,
                    "new_question": "",
                    "useful_indexes": []
                }
            except Exception as e:
                logger.error(f"处理LLM返回结果时出错: {str(e)}, 原始结果: {result}")
                return {
                    "can_ask": False,
                    "new_question": "",
                    "useful_indexes": []
                }

        except Exception as e:
            logger.error(f"调用LLM出错: {str(e)}")
            return {
                "can_ask": False,
                "new_question": "系统暂时无法处理您的问题，请稍后再试。",
                "useful_indexes": []
            }