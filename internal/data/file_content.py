import datetime
import re
from typing import Tuple

from aiapi.search.search_pb2 import FullTextSearchRequest
from internal.data.db import execute_query
from internal.data.db import execute_query_with_params
from internal.utils.convert import highlight_content

# 智能检索范围 数据发现+外发
search_upload_reasons = 1 << 11 | 1 << 10 | 1 << 5 | 1 << 2 | 1 << 1


class FileContentSearch:
    @staticmethod
    def row_to_file(rows, user_map, class_paths, query):
        results = []
        for file in rows:
            class_key = "{}-{}-{}".format(file[3], file[1], file[2])
            class_path = class_paths.get(class_key, "")
            tag_names = []
            if class_path:
                sub_class_paths = class_path.split(".")
                if len(sub_class_paths) > 2:
                    tag_names = [sub_class_paths[1]]
            file_dict = {
                "text": highlight_content(file[0], query),
                "entityTag": file[1],
                "preEntityTag": file[2],
                "fileRelationID": file[3],
                "updatedAt": file[4],
                "title": file[5],
                "userID": file[6],
                "userName": user_map.get(file[6]),
                "fullPath": file[7],
                "tagNames": tag_names,
                "size": file[8],
                "mimeType": file[9],
                "canDoAiProcess": file[10],
            }
            results.append(file_dict)
        return results

    @staticmethod
    def rank_row_to_file(rows, user_map):
        results = []
        for file in rows:
            file_dict = {
                "text": file[1],
                "entityTag": file[2],
                "preEntityTag": file[3],
                "fileRelationID": file[4],
                "updatedAt": file[5],
                "title": file[6],
                "userID": file[7],
                "userName": user_map.get(file[7]),
                "fullPath": file[8],
                "tagNames": []
            }
            results.append(file_dict)
        return results

    @staticmethod
    def relation_row_to_payloads(rows):
        payloads = []
        count = 0
        for file in rows:
            payload = {
                "content": file[0],
                "fileRelationID": file[3],
                "entityTag": file[1],
                "preEntityTag": file[2],
                "name": file[5],
                "userID": file[6],
                "index": -1,
                "chunkIndex": -1,
                "fullPath": file[7]
            }
            payloads.append(payload)
            count += len(file[0])
        return payloads, count

    @classmethod
    def build_sql(cls, request: FullTextSearchRequest, user_ids, user_infos, offset) -> Tuple[str, dict]:
        unique_field = "fm.file_relation_id"
        if request.filterSameFile:
            unique_field = "fm.entity_tag"

        query = f"""
                    SELECT * FROM (
                        SELECT DISTINCT ON({unique_field}) fc.page_content, fm.entity_tag, fm.pre_entity_tag,
                         fm.file_relation_id, fm.meta_updated_at, fm.name, fm.user_id, fm.full_path, fm.size, fm.mime_type, fm.can_do_ai_process
                        FROM file_meta fm
                        INNER JOIN file_content fc ON fc.entity_tag = fm.entity_tag AND fc.pre_entity_tag = fm.pre_entity_tag
                        INNER JOIN file_relation fr ON fr.id = fm.file_relation_id
                    """
        params = {}

        if len(request.classPath) > 0:
            query += """
             INNER JOIN classification_files cf ON cf.entity_tag = fm.entity_tag 
             AND cf.pre_entity_tag = fm.pre_entity_tag AND cf.file_relation_id = fm.file_relation_id
            """
        
        query += """
                   WHERE fr.deleted_at = '0001-01-01T00:00:00Z'
                   """

        if request.query:
            if request.searchType == 1:
                query += f"""
                            AND fm.name_vector @@ plainto_tsquery('testzhcfg', :requestQuery) 
                            """
            else:
                query += f"""
                            AND fc.page_content_vector @@ plainto_tsquery('testzhcfg', :requestQuery) 
                            """
            params["requestQuery"] = request.query

        if request.ownerIDs:
            owner_ids = [str(owner_id) for owner_id in request.ownerIDs]
            owner_ids = list(set(user_ids) & set(owner_ids))
            if not owner_ids:
                return "", {}

            query += """
                            AND fm.user_id IN ({})
                        """.format(','.join(owner_ids))
        elif not user_infos.get('topDeptManager'):
            query += """
                            AND fm.user_id IN ({})
                        """.format(','.join(user_ids))

        if request.fileType:
            query += """
                            AND fm.mime_type ILIKE ANY(:file_types)
                        """
            params["file_types"] = request.fileType.split(",")
        
        if request.path:
            query += """
                            AND fm.full_path ILIKE :path
                        """
            try:
                path = str(re.escape(request.path))
            except Exception as e:
                path = request.path
            params["path"] = "%"+path+"%"

        if request.startTime.seconds > 0 and request.endTime.seconds > 0:
            start_time = datetime.datetime. \
                fromtimestamp(request.startTime.seconds, tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            end_time = datetime.datetime. \
                fromtimestamp(request.endTime.seconds, tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            query += f"""
                            AND fm.meta_updated_at <= :end_time
                            AND fm.meta_updated_at >= :start_time
                        """
            params["start_time"] = start_time
            params["end_time"] = end_time

        if len(request.classPath) > 0:
            query += """
                            AND cf.path <@ :classPath
                        """
            params["classPath"] = request.classPath

        query += f" AND fm.upload_reason & {search_upload_reasons} > 0 "

        query += f"""
                    order by {unique_field}, fm.meta_updated_at desc
                    ) order by meta_updated_at desc LIMIT {request.pageSize} offset {offset};
                    """

        return query, params

    @classmethod
    def build_total_sql(cls, request: FullTextSearchRequest, user_ids, user_infos) -> Tuple[str, dict]:
        unique_field = "fm.file_relation_id"
        if request.filterSameFile:
            unique_field = "fm.entity_tag"

        query = f"""
                    SELECT COUNT(DISTINCT {unique_field}) 
                    FROM file_meta fm
                    INNER JOIN file_content fc ON fc.entity_tag = fm.entity_tag AND fc.pre_entity_tag = fm.pre_entity_tag
                    INNER JOIN file_relation fr ON fr.id = fm.file_relation_id
                    """
        params = {}

        if len(request.classPath) > 0:
            query += """
                    INNER JOIN classification_files cf ON cf.entity_tag = fm.entity_tag 
                    AND cf.pre_entity_tag = fm.pre_entity_tag AND cf.file_relation_id = fm.file_relation_id
                    """

        query += """
                   WHERE fr.deleted_at = '0001-01-01T00:00:00Z'
                   """

        if request.query:
            if request.searchType == 1:
                query += f"""
                            AND fm.name_vector @@ plainto_tsquery('testzhcfg', :requestQuery) 
                            """
            else:
                query += f"""
                            AND fc.page_content_vector @@ plainto_tsquery('testzhcfg', :requestQuery) 
                            """
            params["requestQuery"] = request.query

        if request.ownerIDs:
            owner_ids = [str(owner_id) for owner_id in request.ownerIDs]
            owner_ids = list(set(user_ids) & set(owner_ids))
            if not owner_ids:
                return "", {}

            query += """
                            AND fm.user_id IN ({})
                        """.format(','.join(owner_ids))
        elif not user_infos.get('topDeptManager'):
            query += """
                            AND fm.user_id IN ({})
                        """.format(','.join(user_ids))

        if request.fileType:
            query += """
                            AND fm.mime_type = ANY(:file_types)
                        """
            params["file_types"] = request.fileType.split(",")
        
        if request.path:
            query += """
                            AND fm.full_path ILIKE :path
                        """
            try:
                path = str(re.escape(request.path))
            except Exception as e:
                path = request.path
            params["path"] = "%"+path+"%"

        if request.startTime.seconds > 0 and request.endTime.seconds > 0:
            start_time = datetime.datetime. \
                fromtimestamp(request.startTime.seconds, tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            end_time = datetime.datetime. \
                fromtimestamp(request.endTime.seconds, tz=datetime.timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
            query += f"""
                            AND fm.meta_updated_at <= :end_time
                            AND fm.meta_updated_at >= :start_time
                        """
            params["start_time"] = start_time
            params["end_time"] = end_time

        if len(request.classPath) > 0:
            query += """
                            AND cf.path <@ :classPath
                        """
            params["classPath"] = request.classPath

        query += f" AND fm.upload_reason & {search_upload_reasons} > 0 "

        return query, params

    @classmethod
    def search_files(cls, request: FullTextSearchRequest, user_ids, user_map, user_infos, offset) -> (
            list[dict], int):  # type: ignore
        query, params = cls.build_sql(request, user_ids, user_infos, offset)
        if not query:
            return [], 0

        files = execute_query_with_params(query, params)
        class_paths = cls.batch_get_class_path_by_file(files)

        query, params = cls.build_total_sql(request, user_ids, user_infos)
        if not query:
            return [], 0

        total_result = execute_query_with_params(query, params)

        return cls.row_to_file(files, user_map, class_paths, request.query), total_result[0][0] if total_result else 0
    
    @classmethod
    def get_ts_query(cls, query: str) -> list[str]:
        if not query:
            return []
        try:
            q = "SELECT plainto_tsquery('testzhcfg', :query)"
            params = {"query": query}
            result = execute_query_with_params(q, params)
            cols = str(result[0][0]).split(" & ") # '耐' & '化学试剂' & '化学' & '试剂' & '学' & '试'
            ts_query = []
            for col in cols:
                t = col[1:len(col) - 1]
                if t:
                    ts_query.append(t)
            return ts_query
        except Exception as e:
            return []
    
    @classmethod
    def batch_get_class_path_by_file(cls, files) -> dict[str, str]:
        if len(files) == 0:
            return {}
        
        query = f"""
        SELECT cf.file_relation_id, cf.entity_tag, cf.pre_entity_tag, cf.path
        FROM classification_files cf
        WHERE
        """
        params = {}

        for i, file in enumerate(files):
            if i == len(files) - 1:
                query += f"""(cf.file_relation_id = :fileRelationID{i} AND cf.entity_tag = :entityTag{i} AND cf.pre_entity_tag = :preEntityTag{i})"""
            else:
                query += f"""(cf.file_relation_id = :fileRelationID{i} AND cf.entity_tag = :entityTag{i} AND cf.pre_entity_tag = :preEntityTag{i}) OR """
            params[f"fileRelationID{i}"] = file[3]
            params[f"entityTag{i}"] = file[1]
            params[f"preEntityTag{i}"] = file[2]
        
        rows = execute_query_with_params(query, params)

        results = {}
        for row in rows:
            key = "{}-{}-{}".format(row[0], row[1], row[2])
            path = row[3]
            results[key] = path

        return results

    @classmethod
    def search_files_with_content_by_rank(cls, query_string, user_ids, user_map, page_size, offset) -> list[dict]:
        query_string = " & ".join(query_string)
        query = f"""
            SELECT * FROM (
                SELECT DISTINCT ON(fm.file_relation_id) 
                ts_rank_cd(fc.page_content_vector, plainto_tsquery('testzhcfg', '{query_string}')) AS rank, 
                fc.page_content, fm.entity_tag, fm.pre_entity_tag, 
                fm.file_relation_id, fm.meta_updated_at, fm.name, fm.user_id, fm.full_path
                FROM file_meta fm
                INNER JOIN file_content fc ON fc.entity_tag = fm.entity_tag AND fc.pre_entity_tag = fm.pre_entity_tag
                INNER JOIN file_relation fr ON fr.id = fm.file_relation_id
                WHERE fc.page_content_vector @@ plainto_tsquery('testzhcfg', '{query_string}') AND fr.deleted_at = '0001-01-01T00:00:00Z'
            """

        if len(user_ids) > 0:
            query += """
                    AND user_id IN ({})
                """.format(','.join(map(str, user_ids)))

        query += f" AND fm.upload_reason & {search_upload_reasons} > 0 "

        query += f"""
            order by fm.file_relation_id, fm.meta_updated_at desc
            ) order by rank desc LIMIT {page_size} offset {offset};
            """

        result = execute_query(query)
        return cls.rank_row_to_file(result, user_map)

    @classmethod
    def search_files_with_relation_ids(cls, ids, user_ids) -> (list[dict], int):
        relation_ids = ','.join(map(str, ids))
        query = f"""
                    SELECT DISTINCT ON(fm.file_relation_id)
                    fc.page_content, fm.entity_tag, fm.pre_entity_tag,
                    fm.file_relation_id, fm.meta_updated_at, fm.name, fm.user_id, fm.full_path
                    FROM file_meta fm
                    INNER JOIN file_content fc ON fc.entity_tag = fm.entity_tag AND fc.pre_entity_tag = fm.pre_entity_tag
                    INNER JOIN file_relation fr ON fr.id = fm.file_relation_id
                    WHERE fr.id IN ({relation_ids}) AND fr.deleted_at = '0001-01-01T00:00:00Z'
                """

        if len(user_ids) > 0:
            query += """
                        AND user_id IN ({})
                    """.format(','.join(map(str, user_ids)))

        query += f"""
                order by fm.file_relation_id, fm.meta_updated_at desc;
                """

        result = execute_query(query)
        return cls.relation_row_to_payloads(result)

    @classmethod
    def get_file_content_by_payloads(cls, vs_payloads: list[dict]) -> dict:
        if len(vs_payloads) == 0:
            return {}

        query = f"""
        SELECT DISTINCT ON(page_id, entity_tag, pre_entity_tag) * from file_content WHERE 
        """
        params = {}

        for i, payload in enumerate(vs_payloads):
            if i == len(vs_payloads) - 1:
                query += f"(page_id = :index{i} AND entity_tag = :entityTag{i} AND pre_entity_tag = :preEntityTag{i})"
            else:
                query += f"(page_id = :index{i} AND entity_tag = :entityTag{i} AND pre_entity_tag = :preEntityTag{i}) OR "
            index = payload.get('index')
            if index == -1:
                index = 0
            params[f"index{i}"] = index
            params[f"entityTag{i}"] = payload.get('entityTag')
            params[f"preEntityTag{i}"] = payload.get('preEntityTag')

        rows = execute_query_with_params(query, params)
        if len(rows) == 0:
            return {}
        results = {}
        for row in rows:
            results["{}-{}-{}".format(row[1], row[3], row[4])] = row[2]
        return results
