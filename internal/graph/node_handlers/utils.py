import asyncio
import inspect
import time
from typing import Callable, Awaitable, Any, TypeVar, Dict

T = TypeVar('T')

def potentially_blocking(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
    """装饰器，用于记录函数执行时间并输出日志"""
    async def wrapper(*args: Any, **kwargs: Any) -> T:
        frame = inspect.currentframe()
        filename = inspect.getfile(frame)
        line = inspect.getlineno(frame)
        start = time.time()
        print(f"开始执行: {func.__name__} 在 {filename}:{line}")
        result = await func(*args, **kwargs)
        duration = time.time() - start
        print(f"执行结束: {func.__name__} 在 {filename}:{line}, 耗时: {duration}秒")
        return result
    return wrapper

def get_input_value(inputs_data: Dict[str, Any], param_name: str, default_value: Any = None) -> Any:
    """从输入数据中获取参数值"""
    if not inputs_data or "inputParameters" not in inputs_data:
        return default_value
        
    for param in inputs_data.get("inputParameters", []):
        if param.get("name") == param_name:
            if param["input"]["value"]["type"] == "literal":
                return param["input"]["value"]["content"]
            elif param["input"]["value"]["type"] == "ref":
                # 引用值需要在调用时处理
                return param["input"]["value"]
    
    return default_value

def extract_ref_value(ref_value: Dict[str, Any], node_outputs: Dict[str, Dict[str, Dict[str, Any]]]) -> Any:
    """从引用值中提取实际值"""
    if not ref_value or ref_value.get("type") != "ref":
        return None
        
    content = ref_value.get("content", {})
    source_node = content.get("blockID")
    output_name = content.get("name")
    
    if source_node not in node_outputs or output_name not in node_outputs[source_node]:
        return None
        
    return node_outputs[source_node][output_name]["value"] 