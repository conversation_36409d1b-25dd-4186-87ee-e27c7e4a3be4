import grpc
from typing import Dict, Any

from .base_handler import <PERSON>de<PERSON><PERSON><PERSON>, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from datahelper.logger import get_logger

logger = get_logger(__name__)

class EndNodeHandler(NodeHandler):
    """结束节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理结束节点"""
        final_output = ""
        agent_id = state["agent_id"]
        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
        if ai_agent['id'] == 0:
            final_output = "暂时回答不了这个问题哟"
        else:
            final_output = ai_agent["fallback_msg"]
        
        # 获取节点配置
        node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
        inputs_data = node_data.get("inputs", {})
        input_params = inputs_data.get("inputParameters", [])
        
        # 获取输出内容
        for param in input_params:
            if param["input"]["value"]["type"] == "ref":
                source_node = param["input"]["value"]["content"]["blockID"]
                output_name = param["input"]["value"]["content"]["name"]

                if source_node not in state["node_outputs"] or \
                    output_name not in state["node_outputs"][source_node]:
                    logger.warning(f"Referenced node output not found: {source_node}.{output_name}")
                    continue
                
                final_output = state["node_outputs"][source_node][output_name]["value"]
                # 将最终输出存储在状态中
                state = {**state, "final_output": final_output}
                break
        
        return state 