// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: search/search.proto

package search

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Search_FullTextSearch_FullMethodName      = "/aiapi.search.Search/FullTextSearch"
	Search_QASearch_FullMethodName            = "/aiapi.search.Search/QASearch"
	Search_ChatHistory_FullMethodName         = "/aiapi.search.Search/ChatHistory"
	Search_KnowledgeBaseSearch_FullMethodName = "/aiapi.search.Search/KnowledgeBaseSearch"
	Search_DoChunks_FullMethodName            = "/aiapi.search.Search/DoChunks"
)

// SearchClient is the client API for Search service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SearchClient interface {
	FullTextSearch(ctx context.Context, in *FullTextSearchRequest, opts ...grpc.CallOption) (*FullTextSearchReply, error)
	QASearch(ctx context.Context, in *QASearchRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[QASearchReply], error)
	ChatHistory(ctx context.Context, in *ChatHistoryRequest, opts ...grpc.CallOption) (*ChatHistoryReply, error)
	KnowledgeBaseSearch(ctx context.Context, in *KnowledgeBaseSearchRequest, opts ...grpc.CallOption) (*KnowledgeBaseSearchReply, error)
	DoChunks(ctx context.Context, in *DoChunksRequest, opts ...grpc.CallOption) (*DoChunksReply, error)
}

type searchClient struct {
	cc grpc.ClientConnInterface
}

func NewSearchClient(cc grpc.ClientConnInterface) SearchClient {
	return &searchClient{cc}
}

func (c *searchClient) FullTextSearch(ctx context.Context, in *FullTextSearchRequest, opts ...grpc.CallOption) (*FullTextSearchReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FullTextSearchReply)
	err := c.cc.Invoke(ctx, Search_FullTextSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchClient) QASearch(ctx context.Context, in *QASearchRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[QASearchReply], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Search_ServiceDesc.Streams[0], Search_QASearch_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[QASearchRequest, QASearchReply]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Search_QASearchClient = grpc.ServerStreamingClient[QASearchReply]

func (c *searchClient) ChatHistory(ctx context.Context, in *ChatHistoryRequest, opts ...grpc.CallOption) (*ChatHistoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChatHistoryReply)
	err := c.cc.Invoke(ctx, Search_ChatHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchClient) KnowledgeBaseSearch(ctx context.Context, in *KnowledgeBaseSearchRequest, opts ...grpc.CallOption) (*KnowledgeBaseSearchReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KnowledgeBaseSearchReply)
	err := c.cc.Invoke(ctx, Search_KnowledgeBaseSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchClient) DoChunks(ctx context.Context, in *DoChunksRequest, opts ...grpc.CallOption) (*DoChunksReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DoChunksReply)
	err := c.cc.Invoke(ctx, Search_DoChunks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SearchServer is the server API for Search service.
// All implementations must embed UnimplementedSearchServer
// for forward compatibility.
type SearchServer interface {
	FullTextSearch(context.Context, *FullTextSearchRequest) (*FullTextSearchReply, error)
	QASearch(*QASearchRequest, grpc.ServerStreamingServer[QASearchReply]) error
	ChatHistory(context.Context, *ChatHistoryRequest) (*ChatHistoryReply, error)
	KnowledgeBaseSearch(context.Context, *KnowledgeBaseSearchRequest) (*KnowledgeBaseSearchReply, error)
	DoChunks(context.Context, *DoChunksRequest) (*DoChunksReply, error)
	mustEmbedUnimplementedSearchServer()
}

// UnimplementedSearchServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSearchServer struct{}

func (UnimplementedSearchServer) FullTextSearch(context.Context, *FullTextSearchRequest) (*FullTextSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FullTextSearch not implemented")
}
func (UnimplementedSearchServer) QASearch(*QASearchRequest, grpc.ServerStreamingServer[QASearchReply]) error {
	return status.Errorf(codes.Unimplemented, "method QASearch not implemented")
}
func (UnimplementedSearchServer) ChatHistory(context.Context, *ChatHistoryRequest) (*ChatHistoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChatHistory not implemented")
}
func (UnimplementedSearchServer) KnowledgeBaseSearch(context.Context, *KnowledgeBaseSearchRequest) (*KnowledgeBaseSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KnowledgeBaseSearch not implemented")
}
func (UnimplementedSearchServer) DoChunks(context.Context, *DoChunksRequest) (*DoChunksReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoChunks not implemented")
}
func (UnimplementedSearchServer) mustEmbedUnimplementedSearchServer() {}
func (UnimplementedSearchServer) testEmbeddedByValue()                {}

// UnsafeSearchServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SearchServer will
// result in compilation errors.
type UnsafeSearchServer interface {
	mustEmbedUnimplementedSearchServer()
}

func RegisterSearchServer(s grpc.ServiceRegistrar, srv SearchServer) {
	// If the following call pancis, it indicates UnimplementedSearchServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Search_ServiceDesc, srv)
}

func _Search_FullTextSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FullTextSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServer).FullTextSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Search_FullTextSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServer).FullTextSearch(ctx, req.(*FullTextSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Search_QASearch_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(QASearchRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(SearchServer).QASearch(m, &grpc.GenericServerStream[QASearchRequest, QASearchReply]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Search_QASearchServer = grpc.ServerStreamingServer[QASearchReply]

func _Search_ChatHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServer).ChatHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Search_ChatHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServer).ChatHistory(ctx, req.(*ChatHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Search_KnowledgeBaseSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KnowledgeBaseSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServer).KnowledgeBaseSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Search_KnowledgeBaseSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServer).KnowledgeBaseSearch(ctx, req.(*KnowledgeBaseSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Search_DoChunks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoChunksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServer).DoChunks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Search_DoChunks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServer).DoChunks(ctx, req.(*DoChunksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Search_ServiceDesc is the grpc.ServiceDesc for Search service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Search_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "aiapi.search.Search",
	HandlerType: (*SearchServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FullTextSearch",
			Handler:    _Search_FullTextSearch_Handler,
		},
		{
			MethodName: "ChatHistory",
			Handler:    _Search_ChatHistory_Handler,
		},
		{
			MethodName: "KnowledgeBaseSearch",
			Handler:    _Search_KnowledgeBaseSearch_Handler,
		},
		{
			MethodName: "DoChunks",
			Handler:    _Search_DoChunks_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "QASearch",
			Handler:       _Search_QASearch_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "search/search.proto",
}
