def atomic_question_prompt(document: str) -> str:
    return f"""
    你是一位专业的原子问题提取专家，需要从给定的中文文档中提取出原子问题，您的任务是提取尽可能多的相关问题，并且可以通过给定的内容来回答。请尽量多样化，避免提取重复或相似的问题。确保您的问题包含必要的实体名称，并避免使用代词，如它、他、她、他们、公司、人等。

---Goal---

根据给定的中文文档，准确提取出其中的原子问题，并按照指定格式输出。

---Instructions---

• 仔细阅读并理解文档内容，确保提取的原子问题准确、全面且不遗漏重要信息
• 提取的原子问题应是具体的、明确的，避免模糊或过于宽泛的表述
• 输出格式逐行输出您的答案，每个问题在新行上，没有逐项符号或数字
• 生成的问题数量不要超过10个，优先提取最重要和最有价值的问题
• 如果文档内容较少，可以生成更少的问题，但要确保每个问题都是有意义的

######################
---Examples---
######################
Example 1:

文档：截至2015年12月维基百科一共有280种语言版本，其中英语超过五百万，瑞典语、德语、荷兰语、法语、瓦瑞瓦瑞语、俄语、宿务语、意大利语、越南语和波兰语这十一个语言版本已经有超过100万篇条目，快接近百万的为日语。中文快接近86万另外还有40多个语言版本的超过10万篇文章和超过120个语言版本的维基百科有多于10000个条目。其中在所有维基百科计划中规模最大的语言版本为英语维基百科，英语之后依照条目数量安排的前五名顺序分别是荷兰语维基百科、德语维基百科、瑞典语维基百科以及法语维基百科。
################
Output:
截至2015年12月维基百科有多少种语言版本？
截至2015年12月英语版维基百科的条目数量超过多少？
截至2015年12月哪十一个语言版本的维基百科已经有超过100万篇条目？
在所有维基百科计划中规模最大的语言版本是哪个？
有多少个语言版本的维基百科有多于10000个条目？
有多少个语言版本的维基百科超过10万篇文章？

#############################

#############################
---Real Data---
######################
文档：
{document}
######################
The `Output` should be human text, do not include markdown characters, not unicode characters. Keep the same language as `Query`.
Output:
    """


# 判断上下文是否可以回答问题
def can_answer_question_prompt(question: str, contexts: str, history_questions: list = None) -> str:
    history_str = "历史问题：\n" + "\n".join(history_questions) if history_questions else ""
    
    return f"""
    你是一位严格遵守规则、专业的问答判断专家。你的任务是判断给定的上下文是否能够完整回答特定的问题，并指出哪些片段对回答问题有帮助。

---Goal---

分析上下文内容，判断是否能完整回答问题，并标识有用的内容片段。

---Instructions---

• 仔细分析问题和上下文的内容片段（每个片段都有编号，如<doc index=0>doc0...</doc>、<doc index=1>doc1...</doc>、<doc index=2>doc2...</doc>等）
• 判断所有片段是否共同提供了足够信息来完整回答问题
• 返回格式要求：
  - 返回JSON格式，包含以下字段：
    * useful_indexes: 数组，包含有用片段的编号，必须是上下文中<doc index=n>标签中的n值。注意：
      - 索引值必须严格对应上下文中<doc index=n>中的n值
      - 不允许使用不存在的索引值
      - 索引值必须是整数
      - 如果上下文中没有<doc index=n>标签，则useful_indexes必须是空数组[]
      - 严禁返回上下文中不存在的索引值
      - 如果上下文内容与问题完全不相关，必须返回空数组[]
      - 请确保只使用<doc index=n>中的n值作为索引，不要使用其他值
    * can_answer: 布尔值，表示是否能完整回答问题
    * new_question: 字符串，如果不能完整回答问题时的新问题（如果can_answer为true则为空字符串）
  - 新问题必须：
    * 不与历史问题重复
    * 与原问题主题相关
    * 具体且明确
• 不要返回任何其他内容，不要解释，不要道歉

---Examples---

Example 1:
问题：特斯拉Model 3的性能参数有哪些？
上下文：
<doc index=0>特斯拉Model 3的续航里程为450公里</doc>
<doc index=1>Model 3的百公里加速时间为3.3秒</doc>
<doc index=2>该车型采用了最新的电池技术</doc>
Output:
{{
    "useful_indexes": [0, 1],
    "can_answer": true,
    "new_question": ""
}}

Example 2:
问题：比特币的交易机制是什么？
上下文：
<doc index=0>比特币当前价格为45000美元</doc>
<doc index=1>比特币采用区块链技术</doc>
<doc index=2>比特币交易需要通过矿工验证</doc>
历史问题：
比特币的总量是多少？
Output:
{{
    "useful_indexes": [1, 2],
    "can_answer": false,
    "new_question": "比特币的挖矿难度是如何调节的？"
}}

Example 3:
问题：苹果公司的创始人是谁？
上下文：
<doc index=0>苹果公司由史蒂夫·乔布斯创立于1976年</doc>
<doc index=1>史蒂夫·沃兹尼亚克是联合创始人</doc>
<doc index=2>罗纳德·韦恩也参与了公司的创立</doc>
Output:
{{
    "useful_indexes": [0, 1, 2],
    "can_answer": true,
    "new_question": ""
}}

Example 4:
问题：工作服管理规定的内容是什么？
上下文：
<doc index=0>第三章 工作服管理规定\n第一条 目的\n为推进公司企业文化建设...</doc>
Output:
{{
    "useful_indexes": [0],
    "can_answer": true,
    "new_question": ""
}}

Example 5:
问题：公司食堂的就餐时间是什么时候？
上下文：
<doc>公司食堂开放时间为早上8点到晚上8点</doc>
Output:
{{
    "useful_indexes": [],
    "can_answer": false,
    "new_question": "公司食堂的具体开放时间是什么时候？"
}}

Example 6 (错误示例):
问题：工作服管理规定的内容是什么？
上下文：
<doc index=0>第三章 工作服管理规定\n第一条 目的\n为推进公司企业文化建设...</doc>
Output:
{{
    "useful_indexes": [0, 3],  # 错误：不应该返回不存在的索引3
    "can_answer": true,
    "new_question": ""
}}

Example 7:
问题：公司今年的销售额是多少？
上下文：
<doc index=0>第三章 工作服管理规定\n第一条 目的\n为推进公司企业文化建设...</doc>
Output:
{{
    "useful_indexes": [],  # 上下文内容与问题完全不相关，返回空数组
    "can_answer": false,
    "new_question": "公司今年的销售额是多少？"
}}

---Real Data---

问题：{question}
上下文：{contexts}
历史问题：{history_str}

######################
The `Output` should be human text, do not include markdown characters, not unicode characters. Keep the same language as `Query`.
Output:
    """