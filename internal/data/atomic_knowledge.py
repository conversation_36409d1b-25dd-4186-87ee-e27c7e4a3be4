import datetime

import pytz
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean, JSON
from internal.data.db import session_scope

Base = declarative_base()

DEFAULT_DELETED_AT = datetime.datetime(1, 1, 1, 8, 0, 0, tzinfo=pytz.FixedOffset(480))  # +0800

class AtomicKnowledge(Base):
    __tablename__ = 'atomic_knowledges'
    id = Column(BigInteger, primary_key=True)
    file_relation_id = Column(BigInteger, nullable=False)
    chunk_index = Column(BigInteger, nullable=False)
    chunk_size = Column(BigInteger, nullable=False)
    index = Column(BigInteger, nullable=False)
    knowledge = Column(Text, nullable=False)
    entity_tag = Column(String(255), nullable=False)
    pre_entity_tag = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False)
    deleted_at = Column(TIMESTAMP(timezone=True), default=DEFAULT_DELETED_AT)

    @classmethod
    def add_atomic_knowledge(cls, file_relation_id, chunk_index, chunk_size, index, knowledge, entity_tag, pre_entity_tag):
        with session_scope() as session:
            session.add(cls(
                file_relation_id=file_relation_id,
                chunk_index=chunk_index,
                chunk_size=chunk_size,
                index=index,
                knowledge=knowledge,
                entity_tag=entity_tag,
                pre_entity_tag=pre_entity_tag,
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            ))

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}

        
    

