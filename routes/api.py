from flask import Blueprint, jsonify, request
from datahelper.logger import get_logger

from internal.data.data import DataRepo

api_bp = Blueprint('api', __name__)
logger = get_logger(__name__)
repo = DataRepo()

# 模拟用户数据
users = [
    {"id": 1, "name": "张三", "email": "<PERSON><PERSON><PERSON>@example.com"},
    {"id": 2, "name": "李四", "email": "<EMAIL>"},
    {"id": 3, "name": "王五", "email": "<EMAIL>"}
]

@api_bp.route('/hello', methods=['GET'])
def hello():
    try:
        name = request.args.get('name', 'World')
        logger.info(f"访问 /api/hello 接口，参数: name={name}")
        return jsonify({"message": f"Hello, {name}!", "status": "success"})
    except Exception as e:
        logger.error(f"访问 /api/hello 接口出错: {str(e)}")
        return jsonify({"error": str(e), "status": "error"}), 500

@api_bp.route('/echo', methods=['POST'])
def echo():
    try:
        data = request.json
        if not data:
            return jsonify({"error": "无效的JSON数据", "status": "error"}), 400
        logger.info(f"访问 /api/echo 接口，数据: {data}")
        return jsonify({"data": data, "status": "success"})
    except Exception as e:
        logger.error(f"访问 /api/echo 接口出错: {str(e)}")
        return jsonify({"error": str(e), "status": "error"}), 500

@api_bp.route('/kb_search', methods=['POST'])
def kb_search():
    try:
        data = request.json
        if not data:
            return jsonify({"error": "无效的JSON数据", "status": "error"}), 400
        question = data.get('question')
        user_id = data.get('user_id')
        kb_ids = data.get('kb_ids')
        res = repo.knowledge_base_search(question,kb_ids,[], rerank_threshold=0.1)
        return jsonify({"data": res, "status": "success"})
    except Exception as e:
        return jsonify({"error": str(e), "status": "error"}), 500
    
@api_bp.route('/search_by_file_relation_id', methods=['POST'])
def search_by_file_relation_ids():
    try:
        data = request.json
        if not data:
            return jsonify({"error": "无效的JSON数据", "status": "error"}), 400
        file_relation_ids = data.get('file_relation_ids')
        query = data.get('query')
        files, useful_chunk_content = repo.multi_file_search_v2(query, file_relation_ids, [])
        return jsonify({"files": files, "useful_chunk_content": useful_chunk_content, "status": "success"})
    except Exception as e:
        return jsonify({"error": str(e), "status": "error"}), 500
    
    
@api_bp.route('/atomic_search', methods=['POST'])
def atomic_search():
    try:
        data = request.json
        if not data:
            return jsonify({"error": "无效的JSON数据", "status": "error"}), 400
        query = data.get('query')
        kb_ids = data.get('kb_ids')
        res = repo.atomic_search(query, kb_ids, [])
        return jsonify({"data": res, "status": "success"})
    except Exception as e:
        return jsonify({"error": str(e), "status": "error"}), 500
