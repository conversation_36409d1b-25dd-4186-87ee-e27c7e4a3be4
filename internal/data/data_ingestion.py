import requests
from config.config import config
from datahelper.logger import get_logger

logger = get_logger(__name__)

class DataIngestion:
    def __init__(self):
       self.address = config.get_naming_instance("data-ingestion.http")

    def ingest_atomic_question(self, atomic_question_metadatas: list[dict]):
        if self.address == "":
            logger.error("failed to get data-ingestion host")
            return None
        try:
            resp = requests.post("http://{}/atomic-questions".format(self.address),
                                 json = atomic_question_metadatas)
            if resp.status_code != 200:
                logger.error("ingest atomic question, resp: {}".format(resp.text))
                return None
        except Exception as e:
            logger.error("ingest atomic question, error: {}".format(e))
            return None
        return None

    def ingest_question_semantic_cache(self, question_semantic_cache_metadata: dict):
        if self.address == "":
            logger.error("failed to get data-ingestion host")
            return None
        try:
            resp = requests.post("http://{}/semantic-cache".format(self.address),
                                 json = question_semantic_cache_metadata)
            if resp.status_code != 200:
                logger.error("failed to ingest question semantic cache, resp: {}".format(resp.text))
                return None
        except Exception as e:
            logger.error("failed to ingest question semantic cache, error: {}".format(e))
            return None
        return None