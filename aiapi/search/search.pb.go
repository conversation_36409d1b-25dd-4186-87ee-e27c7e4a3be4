// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: search/search.proto

package search

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type QAStatus int32

const (
	QAStatus_RUNNING QAStatus = 0
	QAStatus_STOPPED QAStatus = 1
)

// Enum value maps for QAStatus.
var (
	QAStatus_name = map[int32]string{
		0: "RUNNING",
		1: "STOPPED",
	}
	QAStatus_value = map[string]int32{
		"RUNNING": 0,
		"STOPPED": 1,
	}
)

func (x QAStatus) Enum() *QAStatus {
	p := new(QAStatus)
	*p = x
	return p
}

func (x QAStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QAStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_search_search_proto_enumTypes[0].Descriptor()
}

func (QAStatus) Type() protoreflect.EnumType {
	return &file_search_search_proto_enumTypes[0]
}

func (x QAStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QAStatus.Descriptor instead.
func (QAStatus) EnumDescriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{0}
}

type QAReplyType int32

const (
	QAReplyType_CONTENT           QAReplyType = 0
	QAReplyType_ABSTRACT_PROCESS  QAReplyType = 1
	QAReplyType_TOO_MANY_REQUESTS QAReplyType = 2
)

// Enum value maps for QAReplyType.
var (
	QAReplyType_name = map[int32]string{
		0: "CONTENT",
		1: "ABSTRACT_PROCESS",
		2: "TOO_MANY_REQUESTS",
	}
	QAReplyType_value = map[string]int32{
		"CONTENT":           0,
		"ABSTRACT_PROCESS":  1,
		"TOO_MANY_REQUESTS": 2,
	}
)

func (x QAReplyType) Enum() *QAReplyType {
	p := new(QAReplyType)
	*p = x
	return p
}

func (x QAReplyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QAReplyType) Descriptor() protoreflect.EnumDescriptor {
	return file_search_search_proto_enumTypes[1].Descriptor()
}

func (QAReplyType) Type() protoreflect.EnumType {
	return &file_search_search_proto_enumTypes[1]
}

func (x QAReplyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QAReplyType.Descriptor instead.
func (QAReplyType) EnumDescriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{1}
}

type FullTextSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query          string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	SearchType     int64                  `protobuf:"varint,2,opt,name=searchType,proto3" json:"searchType,omitempty"`
	FileType       string                 `protobuf:"bytes,3,opt,name=fileType,proto3" json:"fileType,omitempty"`
	OwnerIDs       []int64                `protobuf:"varint,4,rep,packed,name=ownerIDs,proto3" json:"ownerIDs,omitempty"`
	StartTime      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`
	PageNum        int64                  `protobuf:"varint,9,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize       int64                  `protobuf:"varint,10,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ClassPath      string                 `protobuf:"bytes,11,opt,name=classPath,proto3" json:"classPath,omitempty"`
	Path           string                 `protobuf:"bytes,12,opt,name=path,proto3" json:"path,omitempty"`
	FilterSameFile bool                   `protobuf:"varint,13,opt,name=filterSameFile,proto3" json:"filterSameFile,omitempty"`
}

func (x *FullTextSearchRequest) Reset() {
	*x = FullTextSearchRequest{}
	mi := &file_search_search_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullTextSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTextSearchRequest) ProtoMessage() {}

func (x *FullTextSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTextSearchRequest.ProtoReflect.Descriptor instead.
func (*FullTextSearchRequest) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{0}
}

func (x *FullTextSearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *FullTextSearchRequest) GetSearchType() int64 {
	if x != nil {
		return x.SearchType
	}
	return 0
}

func (x *FullTextSearchRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FullTextSearchRequest) GetOwnerIDs() []int64 {
	if x != nil {
		return x.OwnerIDs
	}
	return nil
}

func (x *FullTextSearchRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *FullTextSearchRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *FullTextSearchRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *FullTextSearchRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FullTextSearchRequest) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

func (x *FullTextSearchRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FullTextSearchRequest) GetFilterSameFile() bool {
	if x != nil {
		return x.FilterSameFile
	}
	return false
}

type Documents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text           string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	EntityTag      string                 `protobuf:"bytes,2,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string                 `protobuf:"bytes,3,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	FileRelationID int64                  `protobuf:"varint,4,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	Title          string                 `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	UserID         int64                  `protobuf:"varint,7,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName       string                 `protobuf:"bytes,8,opt,name=userName,proto3" json:"userName,omitempty"`
	FullPath       string                 `protobuf:"bytes,9,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	TagNames       []string               `protobuf:"bytes,10,rep,name=tagNames,proto3" json:"tagNames,omitempty"`
	Size           int64                  `protobuf:"varint,11,opt,name=size,proto3" json:"size,omitempty"`
	MimeType       string                 `protobuf:"bytes,12,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	CanDoAiProcess bool                   `protobuf:"varint,13,opt,name=canDoAiProcess,proto3" json:"canDoAiProcess,omitempty"`
}

func (x *Documents) Reset() {
	*x = Documents{}
	mi := &file_search_search_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Documents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Documents) ProtoMessage() {}

func (x *Documents) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Documents.ProtoReflect.Descriptor instead.
func (*Documents) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{1}
}

func (x *Documents) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Documents) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *Documents) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *Documents) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *Documents) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Documents) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Documents) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *Documents) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Documents) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *Documents) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

func (x *Documents) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Documents) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *Documents) GetCanDoAiProcess() bool {
	if x != nil {
		return x.CanDoAiProcess
	}
	return false
}

type FullTextSearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query    string       `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	TsQuery  []string     `protobuf:"bytes,6,rep,name=tsQuery,proto3" json:"tsQuery,omitempty"`
	PageNum  int64        `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64        `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Refs     []*Documents `protobuf:"bytes,4,rep,name=refs,proto3" json:"refs,omitempty"`
	Total    int64        `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *FullTextSearchReply) Reset() {
	*x = FullTextSearchReply{}
	mi := &file_search_search_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullTextSearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTextSearchReply) ProtoMessage() {}

func (x *FullTextSearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTextSearchReply.ProtoReflect.Descriptor instead.
func (*FullTextSearchReply) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{2}
}

func (x *FullTextSearchReply) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *FullTextSearchReply) GetTsQuery() []string {
	if x != nil {
		return x.TsQuery
	}
	return nil
}

func (x *FullTextSearchReply) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *FullTextSearchReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FullTextSearchReply) GetRefs() []*Documents {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *FullTextSearchReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type QASearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query           string  `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	RoundID         int64   `protobuf:"varint,2,opt,name=roundID,proto3" json:"roundID,omitempty"`
	FileRelationIDs []int64 `protobuf:"varint,3,rep,packed,name=fileRelationIDs,proto3" json:"fileRelationIDs,omitempty"`
}

func (x *QASearchRequest) Reset() {
	*x = QASearchRequest{}
	mi := &file_search_search_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QASearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QASearchRequest) ProtoMessage() {}

func (x *QASearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QASearchRequest.ProtoReflect.Descriptor instead.
func (*QASearchRequest) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{3}
}

func (x *QASearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *QASearchRequest) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *QASearchRequest) GetFileRelationIDs() []int64 {
	if x != nil {
		return x.FileRelationIDs
	}
	return nil
}

type QASearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoundID  int64          `protobuf:"varint,1,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Content  string         `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Status   int64          `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	Payloads []*ChatPayload `protobuf:"bytes,4,rep,name=payloads,proto3" json:"payloads,omitempty"`
	Type     int64          `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *QASearchReply) Reset() {
	*x = QASearchReply{}
	mi := &file_search_search_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QASearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QASearchReply) ProtoMessage() {}

func (x *QASearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QASearchReply.ProtoReflect.Descriptor instead.
func (*QASearchReply) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{4}
}

func (x *QASearchReply) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *QASearchReply) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *QASearchReply) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *QASearchReply) GetPayloads() []*ChatPayload {
	if x != nil {
		return x.Payloads
	}
	return nil
}

func (x *QASearchReply) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

type ChatHistoryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageSize int64 `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	PageNum  int64 `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
}

func (x *ChatHistoryRequest) Reset() {
	*x = ChatHistoryRequest{}
	mi := &file_search_search_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatHistoryRequest) ProtoMessage() {}

func (x *ChatHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatHistoryRequest.ProtoReflect.Descriptor instead.
func (*ChatHistoryRequest) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{5}
}

func (x *ChatHistoryRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ChatHistoryRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ChatHistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID    int64                  `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	TenantID  int64                  `protobuf:"varint,2,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
	RoundID   int64                  `protobuf:"varint,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Query     string                 `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	Answer    string                 `protobuf:"bytes,5,opt,name=answer,proto3" json:"answer,omitempty"`
	Payloads  []*ChatPayload         `protobuf:"bytes,6,rep,name=payloads,proto3" json:"payloads,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *ChatHistory) Reset() {
	*x = ChatHistory{}
	mi := &file_search_search_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatHistory) ProtoMessage() {}

func (x *ChatHistory) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatHistory.ProtoReflect.Descriptor instead.
func (*ChatHistory) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{6}
}

func (x *ChatHistory) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *ChatHistory) GetTenantID() int64 {
	if x != nil {
		return x.TenantID
	}
	return 0
}

func (x *ChatHistory) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *ChatHistory) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ChatHistory) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *ChatHistory) GetPayloads() []*ChatPayload {
	if x != nil {
		return x.Payloads
	}
	return nil
}

func (x *ChatHistory) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type ChatPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	EntityTag      string `protobuf:"bytes,2,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string `protobuf:"bytes,3,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Name           string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Size           int64  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	UserID         int64  `protobuf:"varint,6,opt,name=userID,proto3" json:"userID,omitempty"`
	MimeType       string `protobuf:"bytes,7,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	Index          int64  `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64  `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	ChunkSize      int64  `protobuf:"varint,10,opt,name=chunkSize,proto3" json:"chunkSize,omitempty"`
}

func (x *ChatPayload) Reset() {
	*x = ChatPayload{}
	mi := &file_search_search_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatPayload) ProtoMessage() {}

func (x *ChatPayload) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatPayload.ProtoReflect.Descriptor instead.
func (*ChatPayload) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{7}
}

func (x *ChatPayload) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ChatPayload) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ChatPayload) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ChatPayload) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatPayload) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ChatPayload) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *ChatPayload) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *ChatPayload) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *ChatPayload) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *ChatPayload) GetChunkSize() int64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

type ChatHistoryReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Histories []*ChatHistory `protobuf:"bytes,1,rep,name=histories,proto3" json:"histories,omitempty"`
}

func (x *ChatHistoryReply) Reset() {
	*x = ChatHistoryReply{}
	mi := &file_search_search_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatHistoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatHistoryReply) ProtoMessage() {}

func (x *ChatHistoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatHistoryReply.ProtoReflect.Descriptor instead.
func (*ChatHistoryReply) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{8}
}

func (x *ChatHistoryReply) GetHistories() []*ChatHistory {
	if x != nil {
		return x.Histories
	}
	return nil
}

type KnowledgeBaseSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query            string  `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	KnowledbeBaseIDs []int64 `protobuf:"varint,2,rep,packed,name=knowledbeBaseIDs,proto3" json:"knowledbeBaseIDs,omitempty"`
	MaxNum           int64   `protobuf:"varint,3,opt,name=maxNum,proto3" json:"maxNum,omitempty"`
	RerankThreshold  float32 `protobuf:"fixed32,4,opt,name=rerankThreshold,proto3" json:"rerankThreshold,omitempty"`
}

func (x *KnowledgeBaseSearchRequest) Reset() {
	*x = KnowledgeBaseSearchRequest{}
	mi := &file_search_search_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KnowledgeBaseSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseSearchRequest) ProtoMessage() {}

func (x *KnowledgeBaseSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseSearchRequest.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseSearchRequest) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{9}
}

func (x *KnowledgeBaseSearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *KnowledgeBaseSearchRequest) GetKnowledbeBaseIDs() []int64 {
	if x != nil {
		return x.KnowledbeBaseIDs
	}
	return nil
}

func (x *KnowledgeBaseSearchRequest) GetMaxNum() int64 {
	if x != nil {
		return x.MaxNum
	}
	return 0
}

func (x *KnowledgeBaseSearchRequest) GetRerankThreshold() float32 {
	if x != nil {
		return x.RerankThreshold
	}
	return 0
}

type KnowledgeBaseSearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Contents []string `protobuf:"bytes,1,rep,name=contents,proto3" json:"contents,omitempty"`
}

func (x *KnowledgeBaseSearchReply) Reset() {
	*x = KnowledgeBaseSearchReply{}
	mi := &file_search_search_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KnowledgeBaseSearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseSearchReply) ProtoMessage() {}

func (x *KnowledgeBaseSearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseSearchReply.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseSearchReply) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{10}
}

func (x *KnowledgeBaseSearchReply) GetContents() []string {
	if x != nil {
		return x.Contents
	}
	return nil
}

type DoChunksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content         string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	AdvancedProcess bool   `protobuf:"varint,2,opt,name=advancedProcess,proto3" json:"advancedProcess,omitempty"`
}

func (x *DoChunksRequest) Reset() {
	*x = DoChunksRequest{}
	mi := &file_search_search_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoChunksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoChunksRequest) ProtoMessage() {}

func (x *DoChunksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoChunksRequest.ProtoReflect.Descriptor instead.
func (*DoChunksRequest) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{11}
}

func (x *DoChunksRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *DoChunksRequest) GetAdvancedProcess() bool {
	if x != nil {
		return x.AdvancedProcess
	}
	return false
}

type DoChunksReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chunks []*Chunk `protobuf:"bytes,1,rep,name=chunks,proto3" json:"chunks,omitempty"`
}

func (x *DoChunksReply) Reset() {
	*x = DoChunksReply{}
	mi := &file_search_search_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoChunksReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoChunksReply) ProtoMessage() {}

func (x *DoChunksReply) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoChunksReply.ProtoReflect.Descriptor instead.
func (*DoChunksReply) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{12}
}

func (x *DoChunksReply) GetChunks() []*Chunk {
	if x != nil {
		return x.Chunks
	}
	return nil
}

type Chunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content string   `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Images  []string `protobuf:"bytes,2,rep,name=images,proto3" json:"images,omitempty"`
}

func (x *Chunk) Reset() {
	*x = Chunk{}
	mi := &file_search_search_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chunk) ProtoMessage() {}

func (x *Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_search_search_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chunk.ProtoReflect.Descriptor instead.
func (*Chunk) Descriptor() ([]byte, []int) {
	return file_search_search_proto_rawDescGZIP(), []int{13}
}

func (x *Chunk) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Chunk) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

var File_search_search_proto protoreflect.FileDescriptor

var file_search_search_proto_rawDesc = []byte{
	0x0a, 0x13, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x03, 0x0a, 0x15, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78,
	0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x38, 0x0a, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70,
	0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x61,
	0x6d, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x53, 0x61, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x9d, 0x03, 0x0a,
	0x09, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c,
	0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67,
	0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x38, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x44, 0x6f, 0x41, 0x69, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x61,
	0x6e, 0x44, 0x6f, 0x41, 0x69, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x22, 0xbe, 0x01, 0x0a,
	0x13, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x73,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74, 0x73, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x72, 0x65,
	0x66, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x04, 0x72, 0x65, 0x66, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x6b, 0x0a,
	0x0f, 0x51, 0x41, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44,
	0x12, 0x28, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x0d, 0x51,
	0x41, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x69, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x4a, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x22,
	0xfa, 0x01, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x14, 0x0a,
	0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x08, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x61,
	0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x08, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xa7, 0x02, 0x0a,
	0x0b, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x26, 0x0a, 0x0e,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68,
	0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x68, 0x75, 0x6e,
	0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x4b, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x74, 0x48, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x09, 0x68, 0x69,
	0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x61,
	0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x09, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x1a, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x62, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x62, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x44, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x0f,
	0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x72, 0x65, 0x72, 0x61, 0x6e, 0x6b, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x36, 0x0a, 0x18, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x55,
	0x0a, 0x0f, 0x44, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x61,
	0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x64, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x22, 0x3c, 0x0a, 0x0d, 0x44, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x06, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x52, 0x06, 0x63, 0x68, 0x75,
	0x6e, 0x6b, 0x73, 0x22, 0x39, 0x0a, 0x05, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x2a, 0x24,
	0x0a, 0x08, 0x51, 0x41, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55,
	0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f, 0x50, 0x50,
	0x45, 0x44, 0x10, 0x01, 0x2a, 0x47, 0x0a, 0x0b, 0x51, 0x41, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x41, 0x42, 0x53, 0x54, 0x52, 0x41, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41,
	0x4e, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x10, 0x02, 0x32, 0xae, 0x03,
	0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x58, 0x0a, 0x0e, 0x46, 0x75, 0x6c, 0x6c,
	0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x23, 0x2e, 0x61, 0x69, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65,
	0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x46,
	0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x48, 0x0a, 0x08, 0x51, 0x41, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1d,
	0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x51, 0x41,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x51, 0x41, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x30, 0x01, 0x12, 0x4f, 0x0a, 0x0b,
	0x43, 0x68, 0x61, 0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x2e, 0x61, 0x69,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x43, 0x68, 0x61,
	0x74, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x67, 0x0a,
	0x13, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x12, 0x28, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26,
	0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x46, 0x0a, 0x08, 0x44, 0x6f, 0x43, 0x68, 0x75, 0x6e,
	0x6b, 0x73, 0x12, 0x1d, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x2e, 0x44, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x69, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x2e, 0x44, 0x6f, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x42, 0x3e,
	0x5a, 0x3c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74,
	0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x61, 0x70, 0x69,
	0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x3b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_search_search_proto_rawDescOnce sync.Once
	file_search_search_proto_rawDescData = file_search_search_proto_rawDesc
)

func file_search_search_proto_rawDescGZIP() []byte {
	file_search_search_proto_rawDescOnce.Do(func() {
		file_search_search_proto_rawDescData = protoimpl.X.CompressGZIP(file_search_search_proto_rawDescData)
	})
	return file_search_search_proto_rawDescData
}

var file_search_search_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_search_search_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_search_search_proto_goTypes = []any{
	(QAStatus)(0),                      // 0: aiapi.search.QAStatus
	(QAReplyType)(0),                   // 1: aiapi.search.QAReplyType
	(*FullTextSearchRequest)(nil),      // 2: aiapi.search.FullTextSearchRequest
	(*Documents)(nil),                  // 3: aiapi.search.Documents
	(*FullTextSearchReply)(nil),        // 4: aiapi.search.FullTextSearchReply
	(*QASearchRequest)(nil),            // 5: aiapi.search.QASearchRequest
	(*QASearchReply)(nil),              // 6: aiapi.search.QASearchReply
	(*ChatHistoryRequest)(nil),         // 7: aiapi.search.ChatHistoryRequest
	(*ChatHistory)(nil),                // 8: aiapi.search.ChatHistory
	(*ChatPayload)(nil),                // 9: aiapi.search.ChatPayload
	(*ChatHistoryReply)(nil),           // 10: aiapi.search.ChatHistoryReply
	(*KnowledgeBaseSearchRequest)(nil), // 11: aiapi.search.KnowledgeBaseSearchRequest
	(*KnowledgeBaseSearchReply)(nil),   // 12: aiapi.search.KnowledgeBaseSearchReply
	(*DoChunksRequest)(nil),            // 13: aiapi.search.DoChunksRequest
	(*DoChunksReply)(nil),              // 14: aiapi.search.DoChunksReply
	(*Chunk)(nil),                      // 15: aiapi.search.Chunk
	(*timestamppb.Timestamp)(nil),      // 16: google.protobuf.Timestamp
}
var file_search_search_proto_depIdxs = []int32{
	16, // 0: aiapi.search.FullTextSearchRequest.startTime:type_name -> google.protobuf.Timestamp
	16, // 1: aiapi.search.FullTextSearchRequest.endTime:type_name -> google.protobuf.Timestamp
	16, // 2: aiapi.search.Documents.updatedAt:type_name -> google.protobuf.Timestamp
	3,  // 3: aiapi.search.FullTextSearchReply.refs:type_name -> aiapi.search.Documents
	9,  // 4: aiapi.search.QASearchReply.payloads:type_name -> aiapi.search.ChatPayload
	9,  // 5: aiapi.search.ChatHistory.payloads:type_name -> aiapi.search.ChatPayload
	16, // 6: aiapi.search.ChatHistory.createdAt:type_name -> google.protobuf.Timestamp
	8,  // 7: aiapi.search.ChatHistoryReply.histories:type_name -> aiapi.search.ChatHistory
	15, // 8: aiapi.search.DoChunksReply.chunks:type_name -> aiapi.search.Chunk
	2,  // 9: aiapi.search.Search.FullTextSearch:input_type -> aiapi.search.FullTextSearchRequest
	5,  // 10: aiapi.search.Search.QASearch:input_type -> aiapi.search.QASearchRequest
	7,  // 11: aiapi.search.Search.ChatHistory:input_type -> aiapi.search.ChatHistoryRequest
	11, // 12: aiapi.search.Search.KnowledgeBaseSearch:input_type -> aiapi.search.KnowledgeBaseSearchRequest
	13, // 13: aiapi.search.Search.DoChunks:input_type -> aiapi.search.DoChunksRequest
	4,  // 14: aiapi.search.Search.FullTextSearch:output_type -> aiapi.search.FullTextSearchReply
	6,  // 15: aiapi.search.Search.QASearch:output_type -> aiapi.search.QASearchReply
	10, // 16: aiapi.search.Search.ChatHistory:output_type -> aiapi.search.ChatHistoryReply
	12, // 17: aiapi.search.Search.KnowledgeBaseSearch:output_type -> aiapi.search.KnowledgeBaseSearchReply
	14, // 18: aiapi.search.Search.DoChunks:output_type -> aiapi.search.DoChunksReply
	14, // [14:19] is the sub-list for method output_type
	9,  // [9:14] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_search_search_proto_init() }
func file_search_search_proto_init() {
	if File_search_search_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_search_search_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_search_search_proto_goTypes,
		DependencyIndexes: file_search_search_proto_depIdxs,
		EnumInfos:         file_search_search_proto_enumTypes,
		MessageInfos:      file_search_search_proto_msgTypes,
	}.Build()
	File_search_search_proto = out.File
	file_search_search_proto_rawDesc = nil
	file_search_search_proto_goTypes = nil
	file_search_search_proto_depIdxs = nil
}
