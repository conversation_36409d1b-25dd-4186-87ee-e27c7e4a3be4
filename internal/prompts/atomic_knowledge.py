

def atomic_knowledge_prompt(document: str) -> str:
    return f"""
    你是一位专业的知识提取专家，需要从给定的中文文档中提取出原子知识，这里的原子知识指的是不可再分的最小知识单元，可以是一个概念、一个事实、一种属性等。

---Goal---

根据给定的中文文档，准确提取出其中的原子知识，并按照指定格式输出。

---Instructions---

• 仔细阅读并理解文档内容，确保提取的原子知识准确、全面且不遗漏重要信息
• 提取的原子知识应是具体的、明确的，避免模糊或过于宽泛的表述
• 输出的格式为JSON数组，每个元素是一个对象，包含以下两个键：
  • "knowledge"：提取的原子知识内容，类型为字符串
  • "type"：知识的类型，分为"概念"、"事实"、"属性"、"关系"四类

######################
---Examples---
######################
Example 1:

文档：人工智能是一门研究如何使计算机模拟人类智能的学科，主要包括机器学习、深度学习和自然语言处理等领域。机器学习又分为监督学习、无监督学习和强化学习三种类型。
################
Output:
[
  {{
    "knowledge": "人工智能是一门研究如何使计算机模拟人类智能的学科",
    "type": "概念"
  }},
  {{
    "knowledge": "人工智能主要包括机器学习、深度学习和自然语言处理等领域",
    "type": "事实"
  }},
  {{
    "knowledge": "机器学习分为监督学习、无监督学习和强化学习三种类型",
    "type": "属性"
  }}
]
#############################

Example 2:

文档：巴黎是法国的首都，位于塞纳河畔，以其丰富的艺术文化和历史遗迹而闻名。埃菲尔铁塔是巴黎的标志性建筑之一，每年吸引着大量游客。
################
Output:
[
  {{
    "knowledge": "巴黎是法国的首都",
    "type": "事实"
  }},
  {{
    "knowledge": "巴黎位于塞纳河畔",
    "type": "属性"
  }},
  {{
    "knowledge": "巴黎以其丰富的艺术文化和历史遗迹而闻名",
    "type": "属性"
  }},
  {{
    "knowledge": "埃菲尔铁塔是巴黎的标志性建筑之一",
    "type": "关系"
  }},
  {{
    "knowledge": "埃菲尔铁塔每年吸引着大量游客",
    "type": "属性"
  }}
]
#############################

Example 3:

文档：水的化学式是H₂O，由两个氢原子和一个氧原子组成。水在常温常压下是无色、无味、透明的液体，具有良好的溶解性。
################
Output:
[
  {{
    "knowledge": "水的化学式是H₂O",
    "type": "概念"
  }},
  {{
    "knowledge": "水由两个氢原子和一个氧原子组成",
    "type": "属性"
  }},
  {{
    "knowledge": "水在常温常压下是无色、无味、透明的液体",
    "type": "属性"
  }},
  {{
    "knowledge": "水具有良好的溶解性",
    "type": "属性"
  }}
]
#############################

#############################
---Real Data---
######################
文档：
{document}
######################
The `Output` should be human text, do not include markdown characters, not unicode characters. Keep the same language as `Query`.
Output:
    """