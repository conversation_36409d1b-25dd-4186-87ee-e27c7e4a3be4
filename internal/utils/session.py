import json
import grpc
import requests

from config.config import config
from datahelper.logger import get_logger

logger = get_logger(__name__)


async def parse_session(session: str, context: grpc.aio.ServicerContext) -> tuple[int, int]:
    user_info = json.loads(session)
    uid = user_info.get('UserID')
    tenant_id = user_info.get('TenantID')
    if not uid or not tenant_id:
        await context.abort(grpc.StatusCode.PERMISSION_DENIED)

    return uid, tenant_id


def get_managed_users(session: str):
    adminHost = config.get_naming_instance("admin.http")
    if adminHost == "":
        logger.error("failed to get admin host")
        return None
    try:
        resp = requests.get("http://{}/admin/user/getManagedUsers".format(adminHost),
                            headers={"x-md-global-authz-session": session})
        if resp.status_code != 200:
            logger.error("get user infos, resp: {}".format(resp.text))
            return None
    except Exception as e:
        logger.error("get user infos, error: {}".format(e))
        return None
    return resp.json()
