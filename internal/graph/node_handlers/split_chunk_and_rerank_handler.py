import grpc
from typing import Dict, Any, List

from internal.utils.convert import to_chunk_payloads_for_agent, to_query_infos

from .base_handler import NodeHandler, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from aiapi.agent.agent_pb2 import CallAgentReply

class SplitChunkAndRerankHandler(NodeHandler):
    """分割块并且rerank处理器"""    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)

    async def handle(self, state: WorkflowState) -> WorkflowState:

        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent="分割块并且rerank节点开始..."))

        node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
        
        # 获取查询参数
        inputs_data = node_data.get("inputs", {})
        input_params = inputs_data.get("inputParameters", [])

        inputs = {}
        for param in input_params:
            if param["input"]["value"]["type"] == "ref":
                source_node = param["input"]["value"]["content"]["blockID"]
                output_name = param["input"]["value"]["content"]["name"]
                inputs[param["name"]] = state["node_outputs"][source_node][output_name]["value"]
            else:
                inputs[param["name"]] = param["input"]["value"]["content"]

        file_relation_ids = inputs.get("file_relation_ids", [])

        agent_id = state["agent_id"]
        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()

        user_ids = []
        if ai_agent["knowledge_base_type"] == 1:
            user_ids = state["user_ids"]

        useful_chunk_content = ""

        payloads, count = self.file_content_client.search_files_with_relation_ids(file_relation_ids, user_ids)

        if count == 0:
            return state
        
        if not state["test_mode"]:  
            for payload in payloads:
                await self.context.write(CallAgentReply(roundID=1, content="", status=1, payloads=to_chunk_payloads_for_agent([payload]), type=0))
        
        if count > self.repo.config.llm_max_token_per_round:
            total_chunks = []
            total_chunk_infos = []
            for i, payload in enumerate(payloads):
                content = payload.get("content")
                # 分割 1024 chunk， 冗余512
                chunk_size = 1024
                overlap = 512
                chunks = []
                chunk_infos = []
                if content:
                    total_length = len(content)
                    effective_chunk_size = chunk_size - overlap
                    if total_length <= chunk_size:
                        chunks.append(f"file_name: {payload.get('name')}\nfile_content: {content}")
                        chunk_infos.append({"name": payload.get("name")})
                    else:
                        for start in range(0, total_length, effective_chunk_size):
                            end = min(start + chunk_size, total_length)
                            chunk = content[start:end]
                            chunks.append(f"file_name: {payload.get('name')}\nfile_content: {chunk}")
                            chunk_infos.append({"name": payload.get("name")})
                            if end == total_length:
                                break
                total_chunks.extend(chunks)
                total_chunk_infos.extend(chunk_infos)
            top_n = self.repo.llm_client.knowledge_rerank(state["question"], total_chunks)
            for i, index in enumerate(top_n):
                chunk_content = f"({i})<file_name>{total_chunk_infos[index].get('name')}</file_name><file_content>{total_chunks[index]}</file_content>; "
                if len(useful_chunk_content) + len(chunk_content) + 5000 > self.repo.config.llm_max_token_per_round:
                    break
                useful_chunk_content += chunk_content
                if not state["test_mode"]:
                    await self.context.write(CallAgentReply(type=0, debugContent="检索到有效chunk: "))
                    await self.context.write(CallAgentReply(type=0, debugContent=f"({i})<file_name>{total_chunk_infos[index].get('name')}</file_name><file_content>{total_chunks[index][:200]}</file_content>; "))
            if len(top_n) == 0:
                for i, chunk in enumerate(total_chunks):
                    chunk_content = f"({i})<file_name>{total_chunk_infos[i].get('name')}</file_name><file_content>{total_chunks[i]}</file_content>; "
                    if len(useful_chunk_content) + len(chunk_content) + 5000 > self.repo.config.llm_max_token_per_round:
                        break
                    useful_chunk_content += chunk_content
        else:
            useful_chunk_content = to_query_infos(payloads)


        state["node_outputs"][self.node.id] = {
            "output": {
                "value": useful_chunk_content,
                "type": "string"
            }
        }
        
        return state 