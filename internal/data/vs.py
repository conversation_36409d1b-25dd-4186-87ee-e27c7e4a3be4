from collections import OrderedDict
from datetime import datetime, timedelta, timezone
from config.config import config
from qdrant_client import QdrantClient, models
from qdrant_client.conversions import common_types
from internal.data.grpc import embedding_client

from datahelper.logger import get_logger

logger = get_logger(__name__)


class VectorSearch:
    def __init__(self):
        self.embedding_client = embedding_client
        self.config = config
        self.collection_name = config.collection_name
        self.atomic_question_collection_name = config.atomic_question_collection_name
        self.semantic_cache_collection_name = config.semantic_cache_collection_name
        self.limit = config.vs_search_limit
        self.sparse_search_limit = config.vs_sparse_search_limit
        self.dense_search_limit = config.vs_dense_search_limit
        self.client = QdrantClient(
            host=self.config.vs_host,
            port=self.config.vs_port,
            timeout=10
        )
        # TODO docker-compose depend on data-ingestion

        exist = self.client.collection_exists(collection_name=self.atomic_question_collection_name)
        if not exist:
            logger.info(f"create collection: {self.atomic_question_collection_name}")
            self.client.create_collection(
                collection_name=self.atomic_question_collection_name,
                vectors_config={
                    "dense": models.VectorParams(
                        size=1024,
                        distance=models.Distance.COSINE,
                        on_disk=True
                    ),
                },
                sparse_vectors_config={
                    "sparse": models.SparseVectorParams(
                        index=models.SparseIndexParams(on_disk=True),
                        modifier=models.Modifier.IDF,  # Use the IDF modifier to calculate the BM25 score
                    ),
                },
                quantization_config=models.ScalarQuantization(
                    scalar=models.ScalarQuantizationConfig(
                        type=models.ScalarType.INT8,
                        always_ram=False,
                    ),
                ),
                hnsw_config=models.HnswConfigDiff(on_disk=True),
                on_disk_payload=True,
                shard_number=4,
            )
            self.client.create_payload_index(
                collection_name=self.atomic_question_collection_name,
                field_name="fileRelationID",
                field_schema=models.IntegerIndexParams(
                    type=models.IntegerIndexType.INTEGER,
                    lookup=True,
                    range=False,
                    on_disk=True
                ),
            )
            self.client.create_payload_index(
                collection_name=self.atomic_question_collection_name,
                field_name="userID",
                field_schema=models.IntegerIndexParams(
                    type=models.IntegerIndexType.INTEGER,
                    lookup=True,
                    range=False,
                    on_disk=True
                ),
            )
            self.client.create_payload_index(
                collection_name=self.atomic_question_collection_name,
                field_name="entityTag",
                field_schema=models.KeywordIndexParams(
                    type=models.KeywordIndexType.KEYWORD,
                    on_disk=True
                ),
            )
            self.client.create_payload_index(
                collection_name=self.atomic_question_collection_name,
                field_name="knowledgeBaseIDs",
                field_schema=models.IntegerIndexParams(
                    type=models.IntegerIndexType.INTEGER,
                    lookup=True,
                    range=False,
                    on_disk=True
                ),
            )

    def search(self, query, user_ids, score_threshold=0.5, with_keyword_prefetch=False) -> common_types.QueryResponse:
        query = list(OrderedDict.fromkeys(query))
        if len(user_ids) == 0:
            prefetch_filter = None
        else:
            prefetch_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key="userID",
                        match=models.MatchAny(
                            any=user_ids,
                        ),
                    )
                ]
            )
        if with_keyword_prefetch:
            if prefetch_filter is not None:
                prefetch_filter.must.append(models.FieldCondition(
                    key="nameKeywords",
                    match=models.MatchAny(
                        any=query,
                    ),
                ))
            else:
                prefetch_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="nameKeywords",
                            match=models.MatchAny(
                                any=query,
                            ),
                        )
                    ]
                )

        return self._search(prefetch_filter,
                            self.embedding_client.embedding(query),
                            score_threshold)

    def search_by_file_relation_ids(self, query, file_relation_ids, score_threshold=0.5) -> common_types.QueryResponse:
        query = list(OrderedDict.fromkeys(query))
        prefetch_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="fileRelationID",
                    match=models.MatchAny(
                        any=file_relation_ids,
                    ),
                )
            ]
        )
        return self._search(prefetch_filter,
                            self.embedding_client.embedding(query),
                            score_threshold)

    def search_by_knowledge_base_ids(self, query, knowledge_base_ids, user_ids, score_threshold=0.1, search_limit=config.vs_search_limit) -> common_types.QueryResponse:
        logger.info(f"knowledge base query: {knowledge_base_ids} {query}")
        query = list(OrderedDict.fromkeys(query))
        if knowledge_base_ids is None or len(knowledge_base_ids) == 0:
            knowledge_base_ids = []
        prefetch_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="knowledgeBaseIDs",
                    match=models.MatchAny(
                        any=knowledge_base_ids,
                    ),
                )
            ]
        )
        if user_ids is not None and len(user_ids) != 0:
             prefetch_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key="userID",
                        match=models.MatchAny(
                            any=user_ids,
                        ),
                    )
                ]
            )
        return self._search(prefetch_filter,
                            self.embedding_client.embedding(query),
                            score_threshold, search_limit)

    def _search(self, prefetch_filter, embeddings, score_threshold, search_limit=config.vs_search_limit):
        prefetch = []
        dense_embeddings = []
        sparse_embeddings = []
        dense_embeddings.extend(embeddings.dense)
        sparse_embeddings.extend(embeddings.sparse)

        for dense_embedding, sparse_embedding in zip(
                dense_embeddings, sparse_embeddings):
            prefetch.append(models.Prefetch(
                query=dense_embedding.denseVector,
                using="dense",
                limit=self.dense_search_limit,
                filter=prefetch_filter))

            prefetch.append(models.Prefetch(
                query=models.SparseVector(indices=sparse_embedding.indices,
                                          values=sparse_embedding.values),
                using="sparse",
                limit=self.sparse_search_limit,
                filter=prefetch_filter
            ))

        return self.client.query_points(
            collection_name=self.collection_name,
            prefetch=prefetch,
            query=models.FusionQuery(fusion=models.Fusion.RRF),
            score_threshold=score_threshold,
            limit=search_limit,
            with_payload=True,
        )
    
    def search_similar_atomic_question(self, queries, knowledge_base_ids, user_ids, score_threshold=0.1):
        prefetch_filter = None
        if len(knowledge_base_ids) != 0:
            prefetch_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="knowledgeBaseIDs",
                            match=models.MatchAny(
                                any=knowledge_base_ids,
                            ),
                        )
                    ]
                )
        if len(user_ids) != 0:
            if prefetch_filter is None:
                prefetch_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key="userID",
                            match=models.MatchAny(
                                any=user_ids,
                            ),
                        )
                    ]
                )
            else:
                prefetch_filter.must.append(
                    models.FieldCondition(
                        key="userID",
                        match=models.MatchAny(
                            any=user_ids,
                        ),
                    )
                )

        embeddings = self.embedding_client.embedding(queries)
        prefetch = []
        dense_embeddings = []
        sparse_embeddings = []
        dense_embeddings.extend(embeddings.dense)
        sparse_embeddings.extend(embeddings.sparse)

        for dense_embedding, sparse_embedding in zip(
                dense_embeddings, sparse_embeddings):
            prefetch.append(models.Prefetch(
                query=dense_embedding.denseVector,
                using="dense",
                limit=500,
                filter=prefetch_filter))

            prefetch.append(models.Prefetch(
                query=models.SparseVector(indices=sparse_embedding.indices,
                                          values=sparse_embedding.values),
                using="sparse",
                limit=300,
                filter=prefetch_filter
            ))

        res = self.client.query_points(
            collection_name=self.atomic_question_collection_name,
            prefetch=prefetch,
            query=models.FusionQuery(fusion=models.Fusion.RRF),
            score_threshold=score_threshold,
            limit=self.limit,
            with_payload=True,
        )
        return res
    
    def search_by_question_semantic_cache(self, question, agentID, score_threshold=0.7):
        fifteen_days_ago = (datetime.now(timezone.utc) - timedelta(days=15)).isoformat().replace("+00:00", "Z")
        prefetch_filter = models.Filter(
                must=[
                    models.FieldCondition(
                        key="agentID",
                        match=models.MatchAny(
                            any=agentID,
                        ),
                    ),
                    models.FieldCondition(
                        key="createdAt",
                        range=models.DatetimeRange(
                            gte=fifteen_days_ago
                        )
                    )
                ]
            )
        
        embeddings = self.embedding_client.embedding([question])
        prefetch = []
        dense_embeddings = []
        sparse_embeddings = []
        dense_embeddings.extend(embeddings.dense)
        sparse_embeddings.extend(embeddings.sparse)
        
        for dense_embedding, sparse_embedding in zip(
                dense_embeddings, sparse_embeddings):
            prefetch.append(models.Prefetch(
                query=dense_embedding.denseVector,
                using="dense",
                limit=300,
                filter=prefetch_filter))
            # prefetch.append(models.Prefetch(
            #     query=models.SparseVector(indices=sparse_embedding.indices,
            #                               values=sparse_embedding.values),
            #     using="sparse",
            #     limit=100,
            #     filter=prefetch_filter
            # ))

        # res = self.client.query_points(
        #     collection_name=self.semantic_cache_collection_name,
        #     prefetch=prefetch,
        #     query=models.FusionQuery(fusion=models.Fusion.RRF),
        #     score_threshold=score_threshold,
        #     limit=1,
        #     with_payload=True,
        # )

        res = self.client.query_points(
            collection_name=self.semantic_cache_collection_name,
            query=list(dense_embedding.denseVector),
            using="dense",
            score_threshold=None,
            limit=5,
            with_payload=True,
            query_filter=prefetch_filter,
        )
        return res
        
