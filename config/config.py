import time

import nacos
import os

import yaml

from datahelper.logger import get_logger
from datahelper.ip import get_local_ip
from threading import Thread

logger = get_logger(__name__)


class Config:
    def __init__(self):
        self.server_addresses = os.environ.get("NACOS_HOST") or "localhost"
        self.namespace = "public"
        self.data_id = "ai-api.yaml"
        self.group = "DEFAULT_GROUP"
        self.client = nacos.NacosClient(self.server_addresses, self.namespace, log_level="Error")
        config_body = yaml.safe_load(self.client.get_config(self.data_id, self.group))
        _vs = config_body.get('vs')
        if _vs:
            self.vs_host = _vs.get("host") or "localhost"
            self.vs_port = _vs.get("port") or 6333
            self.collection_name = _vs.get("collection_name")
            self.atomic_question_collection_name = _vs.get("atomic_question_collection_name")
            self.semantic_cache_collection_name = _vs.get("semantic_cache_collection_name")
            self.vs_search_limit = _vs.get("search_limit") or 10
            self.vs_sparse_search_limit = _vs.get("sparse_search_limit") or 10
            self.vs_dense_search_limit = _vs.get("dense_search_limit") or 10
            self.vs_search_with_file_name = _vs.get("search_with_file_name") or False
        else:
            logger.critical("empty vs db config!")

        _server = config_body.get('server')
        if _server:
            self.port = _server.get("port") or 16006
        else:
            logger.critical("empty server config!")
        _db = config_body.get('db')
        if _db:
            self.db_host = _db.get("host")
            self.db_port = _db.get("port")
            self.db_user = _db.get("username")
            self.db_password = _db.get("password")
            self.db_name = _db.get("dbname")
        else:
            logger.critical("empty db config!")

        _llm = config_body.get("llm")
        if _llm:
            self.llm_api_key = _llm.get("api_key")
            self.llm_host = _llm.get("host")
            self.llm_port = _llm.get("port")
            self.llm_model_name = _llm.get("model_name")
            self.llm_max_token_per_round = _llm.get("max_token_per_round")
            self.llm_avg_token_per_round = _llm.get("avg_token_per_round")
            self.llm_chat_history_poll_size_per_round = _llm.get("chat_history_poll_size_per_round")
            self.llm_chat_limit = _llm.get("chat_limit") or 10
            # OpenAI代理设置
            self.llm_proxy = _llm.get("llm_proxy")
        else:
            logger.critical("empty llm config!")

        _external_llm = config_body.get("external_llm")
        if _external_llm:
            self.external_llm_api_key = _external_llm.get("api_key")
            self.external_llm_model_name = _external_llm.get("model_name")
            self.external_llm_url = _external_llm.get("url")
            self.external_llm_chat_limit = _external_llm.get("chat_limit") or 50
        else:
            logger.critical("empty external_llm config!")

        _redis = config_body.get("redis")
        if _redis:
            self.redis_host = _redis.get("addr")
            self.redis_password = _redis.get("password")
            self.limit_key = _redis.get("limit_key")
            self.public_limit_key = _redis.get("public_limit_key")
        else:
            logger.critical("empty redis config!")

        _kafka = config_body.get("kafka")
        if _kafka:
            self.KAFKA_ADDRESSES = _kafka.get("address")
            self.KAFKA_SESSION_TIMEOUT = _kafka.get("session_timeout")
            self.KAFKA_HANDLE_CHUNK_POOL_SIZE = _kafka.get("handle_chunk_pool_size")
            self.KAFKA_HANDLE_CHUNK_IMMEDIATELY_POOL_SIZE = _kafka.get("handle_chunk_immediately_pool_size")

        self.local_ip = get_local_ip()
        self.t = Thread(target=self.register, daemon=True)
        self.t.start()

    def register(self):
        while True:
            try:
                self.client.add_naming_instance("ai-api.grpc", self.local_ip, self.port,
                                                cluster_name="DEFAULT", weight=1,
                                                metadata='{"version":"v1.0", "kind":"grpc"}', enable=True,
                                                healthy=True)
                self.client.send_heartbeat("ai-api.grpc", self.local_ip, self.port, "DEFAULT", 1)
            except Exception as e:
                logger.warning(e)
            time.sleep(5)

    def get_naming_instance(self, service_name) -> str:
        instances = self.client.list_naming_instance(service_name)
        hosts = instances.get('hosts')
        for host in hosts:
            healthy = host.get('healthy')
            if healthy:
                return "{}:{}".format(host.get('ip'), host.get('port'))
        logger.error("failed to get host address, service_name: {}".format(service_name))
        exit(1)


config = Config()
