import time
from typing import Dict, Any, List
import grpc

from .base_handler import <PERSON>deHand<PERSON>, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, LLMModelType, Workflow
from aiapi.agent.agent_pb2 import CallAgentReply, Usage
from datahelper.logger import get_logger
from metrics import metrics
from transformers import AutoModelForCausalLM, AutoTokenizer
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam


logger = get_logger(__name__)

END_NODE_ID = "999999"

class LLMNodeHandler(NodeHandler):
    """LLM节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)


    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理LLM节点"""
        try:

            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent=f"LLM节点开始..."))

            # 获取兜底回复
            fallback_msg = "以我目前的知识和能力还无法解答这个问题。"
            
            agent_id = state["agent_id"]
            ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
            if ai_agent['id'] == 0:
                return state
            
            enable_search = state["internet_search"]
            thinking = state["thinking"]
            
            # 使用 AI Agent 的兜底回复
            if ai_agent.get('fallback_msg'):
                fallback_msg = ai_agent['fallback_msg']

            model_type = ai_agent.get('model_type')
            model_id = ai_agent.get('model_id')

            node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
            inputs_data = node_data.get("inputs", {})
            input_params = inputs_data.get("inputParameters", [])
            llm_params = inputs_data.get("llmParam", [])
            
            # 构建输入数据
            inputs = {}
            for param in input_params:
                if param["input"]["value"]["type"] == "ref":
                    source_node = param["input"]["value"]["content"]["blockID"]
                    output_name = param["input"]["value"]["content"]["name"]
                    # 检查引用的节点输出是否存在
                    if source_node not in state["node_outputs"] or \
                       output_name not in state["node_outputs"][source_node]:
                        # 给个空值
                        inputs[param["name"]] = ""
                        logger.warning(f"Referenced node output not found: {source_node}.{output_name}")
                        continue
                    inputs[param["name"]] = state["node_outputs"][source_node][output_name]["value"]
                else:
                    inputs[param["name"]] = param["input"]["value"]["content"]

            # 获取 llm 参数
            prompt_template = ""
            llm_model_type = ""
            temperature = 0
            max_tokens = 1024
            
            for param in llm_params:
                if param["name"] == "prompt":
                    prompt_template = param["input"]["value"]["content"]
                elif param["name"] == "modelType":
                    llm_model_type = param["input"]["value"]["content"]
                elif param["name"] == "temperature":
                    temperature = float(param["input"]["value"]["content"])
                elif param["name"] == "maxTokens":
                    max_tokens = int(param["input"]["value"]["content"])
            
            # 替换 prompt 中的变量
            prompt = prompt_template
            for key, value in inputs.items():
                if value is not None:  # 只替换非空值

                    # 内容脱敏
                    if model_type == 2 and model_id > 0 and value != "":

                        # 使用敏感度检查器判断内容是否需要脱敏
                        try:
                            # 判断内容是否需要脱敏
                            if key == "question":
                                sensitivity_result = self.sensitivity_checker.check_content_sensitivity(value)
                                needs_deidentification = sensitivity_result.get("needs_deidentification", True)
                                # 只有当需要脱敏时才进行脱敏处理
                                if needs_deidentification:
                                    logger.info("内容需要脱敏处理")
                                    if not state["test_mode"]:
                                        await self.context.write(CallAgentReply(type=0, debugContent=f"内容需要脱敏处理: {value}"))
                                    value = self.repo.deidentifier.deidentify(value).text
                                else:
                                    logger.info("内容无需脱敏处理")
                                    if not state["test_mode"]:
                                        await self.context.write(CallAgentReply(type=0, debugContent=f"内容无需脱敏处理: {value}"))
                        except Exception as e:
                            logger.warning(f"处理脱敏时出错: {str(e)}")
                            if not state["test_mode"]:
                                await self.context.write(CallAgentReply(type=0, debugContent=f"处理脱敏时出错: {str(e)}"))
                    prompt = prompt.replace("{{" + key + "}}", str(value))

            output = None

            # 获取下一个节点
            next_nodes = self.workflow.get_next_nodes(self.node.id)
            
            resp = ""

            llm_client = self.repo.llm_client.client
            llm_aclient = self.repo.llm_client.aclient
            internal_llm_model_name = self.repo.config.llm_model_name


            
                    
            # 构建消息格式
            if model_type == 2 and model_id > 0:
                if thinking and ai_agent.get("thinking_model_id") > 0 and ai_agent.get("thinking"):
                    model_id = ai_agent.get("thinking_model_id")
                    print(f"使用思考模型: {ai_agent.get('thinking_model_id')}")
                ai_model = self.repo.ai_model_client.get_ai_model_by_id(model_id).to_dict()
                ai_model_detail = self.repo.ai_model_detail_client.get_ai_model_detail_by_id(ai_model.get("model")).to_dict()
                llm_client = self.repo.llm_client.create_llm_client(ai_model_detail.get("url"), ai_model.get("api_key"))
                llm_aclient = self.repo.llm_client.create_llm_aclient(ai_model_detail.get("url"), ai_model.get("api_key"))
                internal_llm_model_name = ai_model_detail.get("model_name")
                print(f"使用外部模型: {internal_llm_model_name}")
                if not state["test_mode"]:
                    await self.context.write(CallAgentReply(type=0, debugContent=f"使用外部模型: {internal_llm_model_name}"))
            else:
                if thinking:
                    prompt = f"{prompt} /think"
                else:
                    prompt = f"{prompt} /no_think"

            # 计算当前消息的token数量
            message = {"role": "user", "content": prompt}
            messages = []
            if state["is_multi_round"] and state["multi_round_context"]:
                messages = state["multi_round_context"]

            # 始终添加新消息
            messages.append(message)
            
            # 如果总token数超过限制，从最旧的消息开始删除
            role_setting_tokens = 0
            if ai_agent.get("role_setting"):
                role_setting = ChatCompletionSystemMessageParam(
                        content=ai_agent.get("role_setting"),
                        role="system"
                    )
                role_setting_tokens = len(ai_agent.get("role_setting").split())
            existing_messages_tokens = sum(len(msg["content"].split()) for msg in messages)
            
            while True:
                if existing_messages_tokens + role_setting_tokens + 5000 < self.repo.config.llm_max_token_per_round:
                    break
                if len(messages) > 1:  # 保留至少一条消息（最新的）
                    messages.pop(0)  # 删除最旧的消息
                else:
                    break  # 如果只剩一条消息，就保留它
            if role_setting_tokens > 0:
                messages.insert(0, role_setting)
            if next_nodes and next_nodes[0].id == END_NODE_ID and not state["test_mode"]:
                # 直接流式返回LLM调用结果
                print("直接流式返回LLM调用结果")
                if llm_model_type == LLMModelType.INTERNAL.value:
                    print("调用内部模型（包含元宝、deepseek、千问Max）")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"调用内部模型（包含元宝、deepseek、千问Max）"))
                    start_time = time.time()
                    outputStream = await llm_aclient.chat.completions.create(
                        model=internal_llm_model_name,
                        # temperature=temperature,
                        messages=messages,
                        stream=True,
                        stream_options={"include_usage": True},
                        max_tokens=max_tokens,
                        # frequency_penalty=0,
                        # presence_penalty=0,
                        extra_body={"enable_search": enable_search, "enable_thinking": thinking},
                    )
                    async for chunk in outputStream:
                        try:

                            if chunk and chunk.usage is not None:
                                print(f"chunk.usage: {chunk.usage}")
                                prompt_tokens = chunk.usage.prompt_tokens
                                completion_tokens = chunk.usage.completion_tokens
                                total_tokens = chunk.usage.total_tokens
                                if not state["test_mode"]:
                                    await self.context.write(CallAgentReply(roundID=1, usage=Usage(promptTokens=prompt_tokens, completionTokens=completion_tokens, totalTokens=total_tokens), status=1, type=0))


                            # 添加全面的空值检查
                            if chunk and hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                                # if chunk.usage is not None:
                                #     print(f"chunk.usage: {chunk.usage}")
                                #     prompt_tokens = chunk.usage.prompt_tokens
                                #     completion_tokens = chunk.usage.completion_tokens
                                #     total_tokens = chunk.usage.total_tokens
                                #     if not state["test_mode"]:
                                #         await self.context.write(CallAgentReply(roundID=1, usage=Usage(promptTokens=prompt_tokens, completionTokens=completion_tokens, totalTokens=total_tokens), status=1, type=0))

                                if hasattr(chunk.choices[0], 'delta') and chunk.choices[0].delta:
                                    reason = ""
                                    content = ""
                                    if hasattr(chunk.choices[0].delta, 'reasoning_content') and chunk.choices[0].delta.reasoning_content:
                                        reason = chunk.choices[0].delta.reasoning_content or ""
                                    else:
                                        content = chunk.choices[0].delta.content or ""
                                    if not state["test_mode"]:
                                        await self.context.write(CallAgentReply(roundID=1, content=content, reason=reason, status=1, type=0))
                                    resp += content
                        except Exception as e:
                            logger.warning(f"处理流式响应块时出错: {str(e)}")
                            continue
                    end_time = time.time()
                    metrics.agent_model_internal(start_time)
                    print(f"内部模型调用耗时: {end_time - start_time}秒")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"内部模型调用耗时: {end_time - start_time}秒"))

                elif llm_model_type == LLMModelType.EXTERNAL.value:
                    print("调用外部模型（内外网融合）")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"调用外部模型（内外网融合）"))
                    start_time = time.time()
                    outputStream = await self.repo.llm_client.aexternal_client.chat.completions.create(
                        model=self.repo.config.external_llm_model_name,
                        messages=messages,
                        stream=True,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        frequency_penalty=0,
                        presence_penalty=0
                    )
                    async for chunk in outputStream:
                        try:
                            # 添加全面的空值检查
                            if chunk and hasattr(chunk, 'choices') and chunk.choices and len(chunk.choices) > 0:
                                if hasattr(chunk.choices[0], 'delta') and chunk.choices[0].delta:
                                    content = chunk.choices[0].delta.content or ""
                                    if not state["test_mode"]:
                                        await self.context.write(CallAgentReply(roundID=1, content=content, status=1, type=0))
                                    resp += content
                        except Exception as e:
                            logger.warning(f"处理流式响应块时出错: {str(e)}")
                            continue
                    end_time = time.time()
                    metrics.agent_model_external(start_time)
                    print(f"外部模型调用耗时: {end_time - start_time}秒")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"外部模型调用耗时: {end_time - start_time}秒"))
                state["is_last_llm_node_connect_end_node"] = True
            else:
                # 调用 LLM
                if llm_model_type == LLMModelType.INTERNAL.value:
                    print("调用内部模型")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"调用内部模型"))
                    start_time = time.time()
                    output = llm_client.chat.completions.create(
                        model=internal_llm_model_name,
                        temperature=temperature,
                        messages=messages,
                        stream=False,
                        max_tokens=max_tokens,
                        frequency_penalty=0,
                        presence_penalty=0
                    )
                    end_time = time.time()
                    metrics.agent_model_internal(start_time)
                    print(f"内部模型调用耗时: {end_time - start_time}秒")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"内部模型调用耗时: {end_time - start_time}秒"))
                elif llm_model_type == LLMModelType.EXTERNAL.value:
                    print("调用外部模型")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"调用外部模型"))
                    start_time = time.time()
                    output = self.repo.llm_client.external_client.chat.completions.create(
                        model=self.repo.config.external_llm_model_name,
                        messages=messages,
                        stream=False,
                        max_tokens=max_tokens,
                        temperature=temperature,
                        frequency_penalty=0,
                        presence_penalty=0
                    )
                    end_time = time.time()
                    metrics.agent_model_external(start_time)
                    print(f"外部模型调用耗时: {end_time - start_time}秒")
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"外部模型调用耗时: {end_time - start_time}秒"))

                resp = output.choices[0].message.content if output else fallback_msg
        except Exception as e:
            logger.error(f"LLM node error: {str(e)}")
            resp = fallback_msg
        
        # 保存输出
        state["node_outputs"][self.node.id] = {
            "output": {
                "value": resp,
                "type": "string"
            }
        }
        
        return state 