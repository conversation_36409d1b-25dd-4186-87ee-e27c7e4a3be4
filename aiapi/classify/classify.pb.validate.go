// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: classify/classify.proto

package classify

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ClassifyRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClassifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClassifyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClassifyRequestMultiError, or nil if none found.
func (m *ClassifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ClassifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for FileRelationID

	if len(errors) > 0 {
		return ClassifyRequestMultiError(errors)
	}

	return nil
}

// ClassifyRequestMultiError is an error wrapping multiple validation errors
// returned by ClassifyRequest.ValidateAll() if the designated constraints
// aren't met.
type ClassifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClassifyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClassifyRequestMultiError) AllErrors() []error { return m }

// ClassifyRequestValidationError is the validation error returned by
// ClassifyRequest.Validate if the designated constraints aren't met.
type ClassifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClassifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClassifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClassifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClassifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClassifyRequestValidationError) ErrorName() string { return "ClassifyRequestValidationError" }

// Error satisfies the builtin error interface
func (e ClassifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClassifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClassifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClassifyRequestValidationError{}

// Validate checks the field values on ClassifyResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClassifyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClassifyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClassifyResponseMultiError, or nil if none found.
func (m *ClassifyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ClassifyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuggestedCategory

	if len(errors) > 0 {
		return ClassifyResponseMultiError(errors)
	}

	return nil
}

// ClassifyResponseMultiError is an error wrapping multiple validation errors
// returned by ClassifyResponse.ValidateAll() if the designated constraints
// aren't met.
type ClassifyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClassifyResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClassifyResponseMultiError) AllErrors() []error { return m }

// ClassifyResponseValidationError is the validation error returned by
// ClassifyResponse.Validate if the designated constraints aren't met.
type ClassifyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClassifyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClassifyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClassifyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClassifyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClassifyResponseValidationError) ErrorName() string { return "ClassifyResponseValidationError" }

// Error satisfies the builtin error interface
func (e ClassifyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClassifyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClassifyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClassifyResponseValidationError{}
