import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiChat(Base):
    __tablename__ = 'ai_chat'
    id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False)
    user_id = Column(BigInteger, nullable=False)
    tenant_id = Column(BigInteger, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False)
    chat_type = Column(SmallInteger, nullable=False)
    agent_id = Column(BigInteger, nullable=False)

    @classmethod
    def get_ai_chat_by_id(cls, id):
        with session_scope() as session:
            result = session.query(cls).filter(cls.id == id).first()
            if result is None:
                raise ValueError(f"AiChat with id {id} not found")
            return result

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'user_id': self.user_id,
            'tenant_id': self.tenant_id,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'chat_type': self.chat_type,
            'agent_id': self.agent_id
        }
        
    

