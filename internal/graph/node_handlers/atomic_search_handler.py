import grpc
from typing import Dict, Any, List

from .base_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from internal.utils.convert import to_chunk_payloads_by_file_meta, to_chunk_payloads_by_file_meta_with_content
from aiapi.agent.agent_pb2 import CallAgentReply

class AtomicSearchNodeHandler(NodeHandler):
    """原子搜索节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理原子搜索节点, 思考过程"""
        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent="原子搜索节点开始..."))

        try:
            agent_id = state["agent_id"]
            ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
            knowledge_base_ids = ai_agent["knowledge_base_ids"]
            
            # 轮次
            round_count = 0
            max_round_count = 3

            context = ""
            useful_contexts = ""
            question = state["question"]
            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent=f"原子搜索问题: {question}"))
            # payloads缓存
            payloads_map = {}
            history_questions = [question]
            can_answer_res = False
            
            while round_count < max_round_count:
                user_ids = []
                if ai_agent["knowledge_base_type"] == 1:
                    user_ids = state["user_ids"]
                contents = self.repo.atomic_search(question, knowledge_base_ids, user_ids)
                round_count += 1
                if len(contents) == 0:
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent="本次原子搜索没有找到相关内容"))
                        print(f"本次原子搜索没有找到相关内容")
                    break
                
                for index, content in enumerate(contents):
                    if payloads_map.get(f"{content.get('entityTag')}-{content.get('preEntityTag')}-{content.get('chunkIndex')}-{content.get('chunkSize')}"):
                        continue
                    context += f"<doc index={index} >{content.get('content')}</doc>"
                    context += "\n"
                    payloads_map[f"{content.get('entityTag')}-{content.get('preEntityTag')}-{content.get('chunkIndex')}-{content.get('chunkSize')}"] = content

                can_answer = self.repo.llm_client.can_answer_question(state["question"], context, history_questions)
                print(f"can_answer: {can_answer}")
                if len(can_answer["useful_indexes"]) > 0:
                    useful_indexes = can_answer["useful_indexes"]
                    show_payloads = []
                    recordIndex = 1
                    for index in useful_indexes:
                        if index < len(contents):
                            content = contents[index]
                            useful_contexts += f"({recordIndex})<file_name>{content.get('title')}</file_name><file_content>{content.get('content')}</file_content>"
                            recordIndex += 1
                            file_meta = self.repo.file_meta_client.get_file_meta(content.get("fileRelationID"), content.get("entityTag"), content.get("preEntityTag"))
                            if file_meta:
                                file_meta = file_meta.to_dict()
                                file_meta["index"] = content.get("index")
                                file_meta["chunk_index"] = content.get("chunkIndex")
                                file_meta["chunk_size"] = content.get("chunkSize")
                                file_meta["imageKeys"] = content.get("imageKeys", [])
                                file_meta["content"] = content.get("content")
                                
                                show_payload_key = f"{file_meta['entity_tag']}-{file_meta['pre_entity_tag']}-{file_meta['file_relation_id']}"
                                if show_payload_key not in [f"{p['entity_tag']}-{p['pre_entity_tag']}-{p['file_relation_id']}" for p in show_payloads]:
                                    show_payloads.append(file_meta)

                    payloads = to_chunk_payloads_by_file_meta_with_content(show_payloads)
                    state["payloads"] = payloads
                    if ai_agent["is_ref_files"] and not state["test_mode"]:         
                        await self.context.write(CallAgentReply(roundID=1, content="", payloads=to_chunk_payloads_by_file_meta(show_payloads), status=1, type=0))
                    if can_answer["can_ask"]:
                        can_answer_res = True
                        break
                    else:
                        print(f"不能回答问题，提出新问题: {can_answer['new_question']}")
                        if not state["test_mode"]:
                            await self.context.write(CallAgentReply(type=0, debugContent=f"不能回答问题，提出新问题: {can_answer['new_question']}"))
                        new_question = can_answer["new_question"]
                        if new_question:
                            question = new_question
                            history_questions.append(new_question)
                        else:
                            break
            
            if can_answer_res == False:
                useful_contexts = ""

            print(f"思考轮次: {round_count}")
            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent=f"思考轮次: {round_count}"))

            if useful_contexts != "":
                # useful_contexts 按200字分割
                useful_contexts_list = [useful_contexts[i:i+200] for i in range(0, len(useful_contexts), 200)]
                for context in useful_contexts_list:
                    if not state["test_mode"]:
                        await self.context.write(CallAgentReply(type=0, debugContent=f"原子搜索结果: {context}"))
            
            state["node_outputs"][self.node.id] = {
                "output": {
                    "value": useful_contexts,
                    "type": "string"
                }
            }
        except Exception as e:
            print(f"原子搜索处理失败: {str(e)}")
            if not state["test_mode"]:
                await self.context.write(CallAgentReply(type=0, debugContent=f"原子搜索处理失败: {str(e)}"))
            # 出错时返回空结果
            state["node_outputs"][self.node.id] = {
                "output": {
                    "value": "",
                    "type": "string"
                }
            }
        
        return state 