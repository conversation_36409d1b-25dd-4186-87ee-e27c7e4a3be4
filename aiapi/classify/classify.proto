syntax = "proto3";

package aiapi.classify;

option go_package = "gitlab.minum.cloud/innovationteam/ai-api/aiapi/classify;classify";

message ClassifyRequest {
  string entityTag = 1;
  string preEntityTag = 2;
  int64 fileRelationID = 3;
  repeated string existing_categories = 4;
}

message ClassifyResponse {
  string suggestedCategory = 1;
}

service ClassifyService {
  rpc DoClassify(ClassifyRequest) returns (ClassifyResponse);
}
