from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class QAStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    RUNNING: _ClassVar[QAStatus]
    STOPPED: _ClassVar[QAStatus]

class QAReplyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    CONTENT: _ClassVar[QAReplyType]
    ABSTRACT_PROCESS: _ClassVar[QAReplyType]
    TOO_MANY_REQUESTS: _ClassVar[QAReplyType]
RUNNING: QAStatus
STOPPED: QAStatus
CONTENT: QAReplyType
ABSTRACT_PROCESS: QAReplyType
TOO_MANY_REQUESTS: QAReplyType

class FullTextSearchRequest(_message.Message):
    __slots__ = ("query", "searchType", "fileType", "ownerIDs", "startTime", "endTime", "pageNum", "pageSize", "classPath", "path", "filterSameFile")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    SEARCHTYPE_FIELD_NUMBER: _ClassVar[int]
    FILETYPE_FIELD_NUMBER: _ClassVar[int]
    OWNERIDS_FIELD_NUMBER: _ClassVar[int]
    STARTTIME_FIELD_NUMBER: _ClassVar[int]
    ENDTIME_FIELD_NUMBER: _ClassVar[int]
    PAGENUM_FIELD_NUMBER: _ClassVar[int]
    PAGESIZE_FIELD_NUMBER: _ClassVar[int]
    CLASSPATH_FIELD_NUMBER: _ClassVar[int]
    PATH_FIELD_NUMBER: _ClassVar[int]
    FILTERSAMEFILE_FIELD_NUMBER: _ClassVar[int]
    query: str
    searchType: int
    fileType: str
    ownerIDs: _containers.RepeatedScalarFieldContainer[int]
    startTime: _timestamp_pb2.Timestamp
    endTime: _timestamp_pb2.Timestamp
    pageNum: int
    pageSize: int
    classPath: str
    path: str
    filterSameFile: bool
    def __init__(self, query: _Optional[str] = ..., searchType: _Optional[int] = ..., fileType: _Optional[str] = ..., ownerIDs: _Optional[_Iterable[int]] = ..., startTime: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., endTime: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., pageNum: _Optional[int] = ..., pageSize: _Optional[int] = ..., classPath: _Optional[str] = ..., path: _Optional[str] = ..., filterSameFile: bool = ...) -> None: ...

class Documents(_message.Message):
    __slots__ = ("text", "entityTag", "preEntityTag", "fileRelationID", "updatedAt", "title", "userID", "userName", "fullPath", "tagNames", "size", "mimeType", "canDoAiProcess")
    TEXT_FIELD_NUMBER: _ClassVar[int]
    ENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    PREENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    FILERELATIONID_FIELD_NUMBER: _ClassVar[int]
    UPDATEDAT_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    USERID_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    FULLPATH_FIELD_NUMBER: _ClassVar[int]
    TAGNAMES_FIELD_NUMBER: _ClassVar[int]
    SIZE_FIELD_NUMBER: _ClassVar[int]
    MIMETYPE_FIELD_NUMBER: _ClassVar[int]
    CANDOAIPROCESS_FIELD_NUMBER: _ClassVar[int]
    text: str
    entityTag: str
    preEntityTag: str
    fileRelationID: int
    updatedAt: _timestamp_pb2.Timestamp
    title: str
    userID: int
    userName: str
    fullPath: str
    tagNames: _containers.RepeatedScalarFieldContainer[str]
    size: int
    mimeType: str
    canDoAiProcess: bool
    def __init__(self, text: _Optional[str] = ..., entityTag: _Optional[str] = ..., preEntityTag: _Optional[str] = ..., fileRelationID: _Optional[int] = ..., updatedAt: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ..., title: _Optional[str] = ..., userID: _Optional[int] = ..., userName: _Optional[str] = ..., fullPath: _Optional[str] = ..., tagNames: _Optional[_Iterable[str]] = ..., size: _Optional[int] = ..., mimeType: _Optional[str] = ..., canDoAiProcess: bool = ...) -> None: ...

class FullTextSearchReply(_message.Message):
    __slots__ = ("query", "tsQuery", "pageNum", "pageSize", "refs", "total")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    TSQUERY_FIELD_NUMBER: _ClassVar[int]
    PAGENUM_FIELD_NUMBER: _ClassVar[int]
    PAGESIZE_FIELD_NUMBER: _ClassVar[int]
    REFS_FIELD_NUMBER: _ClassVar[int]
    TOTAL_FIELD_NUMBER: _ClassVar[int]
    query: str
    tsQuery: _containers.RepeatedScalarFieldContainer[str]
    pageNum: int
    pageSize: int
    refs: _containers.RepeatedCompositeFieldContainer[Documents]
    total: int
    def __init__(self, query: _Optional[str] = ..., tsQuery: _Optional[_Iterable[str]] = ..., pageNum: _Optional[int] = ..., pageSize: _Optional[int] = ..., refs: _Optional[_Iterable[_Union[Documents, _Mapping]]] = ..., total: _Optional[int] = ...) -> None: ...

class QASearchRequest(_message.Message):
    __slots__ = ("query", "roundID", "fileRelationIDs")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    FILERELATIONIDS_FIELD_NUMBER: _ClassVar[int]
    query: str
    roundID: int
    fileRelationIDs: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, query: _Optional[str] = ..., roundID: _Optional[int] = ..., fileRelationIDs: _Optional[_Iterable[int]] = ...) -> None: ...

class QASearchReply(_message.Message):
    __slots__ = ("roundID", "content", "status", "payloads", "type")
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PAYLOADS_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    roundID: int
    content: str
    status: int
    payloads: _containers.RepeatedCompositeFieldContainer[ChatPayload]
    type: int
    def __init__(self, roundID: _Optional[int] = ..., content: _Optional[str] = ..., status: _Optional[int] = ..., payloads: _Optional[_Iterable[_Union[ChatPayload, _Mapping]]] = ..., type: _Optional[int] = ...) -> None: ...

class ChatHistoryRequest(_message.Message):
    __slots__ = ("pageSize", "pageNum")
    PAGESIZE_FIELD_NUMBER: _ClassVar[int]
    PAGENUM_FIELD_NUMBER: _ClassVar[int]
    pageSize: int
    pageNum: int
    def __init__(self, pageSize: _Optional[int] = ..., pageNum: _Optional[int] = ...) -> None: ...

class ChatHistory(_message.Message):
    __slots__ = ("userID", "tenantID", "roundID", "query", "answer", "payloads", "createdAt")
    USERID_FIELD_NUMBER: _ClassVar[int]
    TENANTID_FIELD_NUMBER: _ClassVar[int]
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    QUERY_FIELD_NUMBER: _ClassVar[int]
    ANSWER_FIELD_NUMBER: _ClassVar[int]
    PAYLOADS_FIELD_NUMBER: _ClassVar[int]
    CREATEDAT_FIELD_NUMBER: _ClassVar[int]
    userID: int
    tenantID: int
    roundID: int
    query: str
    answer: str
    payloads: _containers.RepeatedCompositeFieldContainer[ChatPayload]
    createdAt: _timestamp_pb2.Timestamp
    def __init__(self, userID: _Optional[int] = ..., tenantID: _Optional[int] = ..., roundID: _Optional[int] = ..., query: _Optional[str] = ..., answer: _Optional[str] = ..., payloads: _Optional[_Iterable[_Union[ChatPayload, _Mapping]]] = ..., createdAt: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class ChatPayload(_message.Message):
    __slots__ = ("fileRelationID", "entityTag", "preEntityTag", "name", "size", "userID", "mimeType", "index", "chunkIndex", "chunkSize")
    FILERELATIONID_FIELD_NUMBER: _ClassVar[int]
    ENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    PREENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SIZE_FIELD_NUMBER: _ClassVar[int]
    USERID_FIELD_NUMBER: _ClassVar[int]
    MIMETYPE_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    CHUNKINDEX_FIELD_NUMBER: _ClassVar[int]
    CHUNKSIZE_FIELD_NUMBER: _ClassVar[int]
    fileRelationID: int
    entityTag: str
    preEntityTag: str
    name: str
    size: int
    userID: int
    mimeType: str
    index: int
    chunkIndex: int
    chunkSize: int
    def __init__(self, fileRelationID: _Optional[int] = ..., entityTag: _Optional[str] = ..., preEntityTag: _Optional[str] = ..., name: _Optional[str] = ..., size: _Optional[int] = ..., userID: _Optional[int] = ..., mimeType: _Optional[str] = ..., index: _Optional[int] = ..., chunkIndex: _Optional[int] = ..., chunkSize: _Optional[int] = ...) -> None: ...

class ChatHistoryReply(_message.Message):
    __slots__ = ("histories",)
    HISTORIES_FIELD_NUMBER: _ClassVar[int]
    histories: _containers.RepeatedCompositeFieldContainer[ChatHistory]
    def __init__(self, histories: _Optional[_Iterable[_Union[ChatHistory, _Mapping]]] = ...) -> None: ...

class KnowledgeBaseSearchRequest(_message.Message):
    __slots__ = ("query", "knowledbeBaseIDs", "maxNum", "rerankThreshold")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    KNOWLEDBEBASEIDS_FIELD_NUMBER: _ClassVar[int]
    MAXNUM_FIELD_NUMBER: _ClassVar[int]
    RERANKTHRESHOLD_FIELD_NUMBER: _ClassVar[int]
    query: str
    knowledbeBaseIDs: _containers.RepeatedScalarFieldContainer[int]
    maxNum: int
    rerankThreshold: float
    def __init__(self, query: _Optional[str] = ..., knowledbeBaseIDs: _Optional[_Iterable[int]] = ..., maxNum: _Optional[int] = ..., rerankThreshold: _Optional[float] = ...) -> None: ...

class KnowledgeBaseSearchReply(_message.Message):
    __slots__ = ("contents",)
    CONTENTS_FIELD_NUMBER: _ClassVar[int]
    contents: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, contents: _Optional[_Iterable[str]] = ...) -> None: ...

class DoChunksRequest(_message.Message):
    __slots__ = ("content", "advancedProcess")
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    ADVANCEDPROCESS_FIELD_NUMBER: _ClassVar[int]
    content: str
    advancedProcess: bool
    def __init__(self, content: _Optional[str] = ..., advancedProcess: bool = ...) -> None: ...

class DoChunksReply(_message.Message):
    __slots__ = ("chunks",)
    CHUNKS_FIELD_NUMBER: _ClassVar[int]
    chunks: _containers.RepeatedCompositeFieldContainer[Chunk]
    def __init__(self, chunks: _Optional[_Iterable[_Union[Chunk, _Mapping]]] = ...) -> None: ...

class Chunk(_message.Message):
    __slots__ = ("content", "images")
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    content: str
    images: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, content: _Optional[str] = ..., images: _Optional[_Iterable[str]] = ...) -> None: ...
