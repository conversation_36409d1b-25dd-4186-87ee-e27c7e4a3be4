# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: aiapi/health/health.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'aiapi/health/health.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19\x61iapi/health/health.proto\x12\x0c\x61iapi.health\"\x0f\n\rHealthRequest\"\x1e\n\x0bHealthReply\x12\x0f\n\x07message\x18\x01 \x01(\t2I\n\x06Health\x12?\n\x05\x43heck\x12\x1b.aiapi.health.HealthRequest\x1a\x19.aiapi.health.HealthReplyB>Z<gitlab.minum.cloud/innovationteam/ai-api/aiapi/health;healthb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'aiapi.health.health_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z<gitlab.minum.cloud/innovationteam/ai-api/aiapi/health;health'
  _globals['_HEALTHREQUEST']._serialized_start=43
  _globals['_HEALTHREQUEST']._serialized_end=58
  _globals['_HEALTHREPLY']._serialized_start=60
  _globals['_HEALTHREPLY']._serialized_end=90
  _globals['_HEALTH']._serialized_start=92
  _globals['_HEALTH']._serialized_end=165
# @@protoc_insertion_point(module_scope)
