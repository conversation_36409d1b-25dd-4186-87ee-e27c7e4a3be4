# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from aiapi.classify import classify_pb2 as aiapi_dot_classify_dot_classify__pb2

GRPC_GENERATED_VERSION = '1.67.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in aiapi/classify/classify_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ClassifyServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.DoClassify = channel.unary_unary(
                '/aiapi.classify.ClassifyService/DoClassify',
                request_serializer=aiapi_dot_classify_dot_classify__pb2.ClassifyRequest.SerializeToString,
                response_deserializer=aiapi_dot_classify_dot_classify__pb2.ClassifyResponse.FromString,
                _registered_method=True)


class ClassifyServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def DoClassify(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ClassifyServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'DoClassify': grpc.unary_unary_rpc_method_handler(
                    servicer.DoClassify,
                    request_deserializer=aiapi_dot_classify_dot_classify__pb2.ClassifyRequest.FromString,
                    response_serializer=aiapi_dot_classify_dot_classify__pb2.ClassifyResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'aiapi.classify.ClassifyService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('aiapi.classify.ClassifyService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ClassifyService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def DoClassify(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/aiapi.classify.ClassifyService/DoClassify',
            aiapi_dot_classify_dot_classify__pb2.ClassifyRequest.SerializeToString,
            aiapi_dot_classify_dot_classify__pb2.ClassifyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
