import unittest
from internal.data.grpc import embedding_client
from dataembedding.rerank.rerank_pb2 import SentencePair

class TestGRPCClient(unittest.TestCase):
    def test_embed(self):
        reply = embedding_client.embedding(["你好"])
        print(reply)
    
    def test_rerank(self):
        pair = SentencePair(sentenceA="""
强力新材SAP实施项目
用户操作手册
PM保养计划的维护
目录
建立保养计划
业务流程说明
适用范围
本流程适用于强力相关公司，设备年度保养计划的制定，适用于机器设备、检验仪器的管理
本业务流程的主要负责部门和主要负责岗位
设备科（设备工程师）、品保科、研发中心：负责制定年度保养计划；
设备科（机修、电仪班/组）：负责按照保养计划实施；
设备使用部门：负责协商确认可保养的时间；
建立保养计划的基本菜单  IP41
菜单路径	后勤 -> 工厂维护 -> 预防性维护 -> 维护计划 -> 维护计划 -> 创建
事务代码	IP4
操作步骤说明
使用SAP路径以开始任务,也可以在命令框直接输入【事务代码】：IP41，点击或按<Enter>键开始任务：
""", sentenceB="维护计划")
        reply = embedding_client.rerank([pair])
        for score in reply.scores:
            print(score)

    def test_rerank_n(self):
        pair1 = SentencePair(sentenceA="""
强力新材SAP实施项目
用户操作手册
PM保养计划的维护
目录
建立保养计划
业务流程说明
适用范围
本流程适用于强力相关公司，设备年度保养计划的制定，适用于机器设备、检验仪器的管理
本业务流程的主要负责部门和主要负责岗位
设备科（设备工程师）、品保科、研发中心：负责制定年度保养计划；
设备科（机修、电仪班/组）：负责按照保养计划实施；
设备使用部门：负责协商确认可保养的时间；
建立保养计划的基本菜单  IP41
菜单路径	后勤 -> 工厂维护 -> 预防性维护 -> 维护计划 -> 维护计划 -> 创建
事务代码	IP4
操作步骤说明
使用SAP路径以开始任务,也可以在命令框直接输入【事务代码】：IP41，点击或按<Enter>键开始任务：
""", sentenceB="维护计划")
        pair2 = SentencePair(sentenceA="2", sentenceB="2")
        pair3 = SentencePair(sentenceA="3", sentenceB="3")
        pair4 = SentencePair(sentenceA="4", sentenceB="4")
        pair5 = SentencePair(sentenceA="5", sentenceB="5")
        reply = embedding_client.rerank([pair1, pair2, pair3, pair4, pair5])
        indexed_scores = list(enumerate(reply.scores))
        for idx, score in indexed_scores:
            print(f"{idx}: {score}")

        sorted_indices = sorted(indexed_scores, key=lambda x: x[1], reverse=True)
        top_indices = [idx for idx, _ in sorted_indices[:min(4, len(sorted_indices))]]
        print(top_indices)
