# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: aiapi/agent/agent.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'aiapi/agent/agent.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x61iapi/agent/agent.proto\x12\x0b\x61iapi.agent\"\xf8\x01\n\x0b\x43hatPayload\x12\x16\n\x0e\x66ileRelationID\x18\x01 \x01(\x03\x12\x11\n\tentityTag\x18\x02 \x01(\t\x12\x14\n\x0cpreEntityTag\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04size\x18\x05 \x01(\x03\x12\x0e\n\x06userID\x18\x06 \x01(\x03\x12\x10\n\x08mimeType\x18\x07 \x01(\t\x12\r\n\x05index\x18\x08 \x01(\x03\x12\x12\n\nchunkIndex\x18\t \x01(\x03\x12\x11\n\tchunkSize\x18\n \x01(\x03\x12\x0f\n\x07\x63ontent\x18\x0b \x01(\t\x12\x11\n\timageKeys\x18\x0c \x03(\t\x12\x10\n\x08\x66ullPath\x18\r \x01(\t\"\xc2\x01\n\x10\x43\x61llAgentRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0f\n\x07\x61gentID\x18\x02 \x01(\x03\x12\x0f\n\x07roundID\x18\x03 \x01(\x03\x12\x17\n\x0f\x66ileRelationIDs\x18\x04 \x03(\x03\x12\x0e\n\x06\x63hatID\x18\x05 \x01(\x03\x12\x14\n\x0cisMultiRound\x18\x06 \x01(\x08\x12\x14\n\x0c\x61iChatItemID\x18\x07 \x01(\x03\x12\x16\n\x0einternetSearch\x18\x08 \x01(\x08\x12\x10\n\x08thinking\x18\t \x01(\x08\"L\n\x05Usage\x12\x14\n\x0cpromptTokens\x18\x01 \x01(\x03\x12\x18\n\x10\x63ompletionTokens\x18\x02 \x01(\x03\x12\x13\n\x0btotalTokens\x18\x03 \x01(\x03\"\xee\x01\n\x0e\x43\x61llAgentReply\x12\x0f\n\x07roundID\x18\x01 \x01(\x03\x12\x0f\n\x07\x63ontent\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\x03\x12*\n\x08payloads\x18\x04 \x03(\x0b\x32\x18.aiapi.agent.ChatPayload\x12\x0c\n\x04type\x18\x05 \x01(\x03\x12\x0e\n\x06reason\x18\x06 \x01(\t\x12\x14\n\x0c\x64\x65\x62ugContent\x18\x07 \x01(\t\x12!\n\x05usage\x18\x08 \x01(\x0b\x32\x12.aiapi.agent.Usage\x12\x15\n\rcachePayloads\x18\t \x01(\t\x12\x10\n\x08isCached\x18\n \x01(\x08\"G\n\x14\x43\x61llAgentTestRequest\x12\r\n\x05query\x18\x01 \x01(\t\x12\x0f\n\x07\x61gentID\x18\x02 \x01(\x03\x12\x0f\n\x07roundID\x18\x03 \x01(\x03\"Q\n\x12\x43\x61llAgentTestReply\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\x12*\n\x08payloads\x18\x02 \x03(\x0b\x32\x18.aiapi.agent.ChatPayload\"4\n\nCustomRule\x12\x11\n\truleTitle\x18\x01 \x01(\t\x12\x13\n\x0bruleContent\x18\x02 \x01(\t\"j\n\x15\x43ontractReviewRequest\x12,\n\x0b\x63ustomRules\x18\x01 \x03(\x0b\x32\x17.aiapi.agent.CustomRule\x12\x0e\n\x06partyA\x18\x02 \x01(\x08\x12\x13\n\x0b\x66ileContent\x18\x03 \x01(\t\"&\n\x13\x43ontractReviewReply\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\t\"B\n\x1cQuestionSecurityCheckRequest\x12\x10\n\x08question\x18\x01 \x01(\t\x12\x10\n\x08policies\x18\x02 \x03(\t\")\n\x1aQuestionSecurityCheckReply\x12\x0b\n\x03hit\x18\x01 \x01(\x08\"o\n(CheckAndSaveQuestionSemanticCacheRequest\x12\x10\n\x08question\x18\x01 \x01(\t\x12\x0e\n\x06\x61nswer\x18\x02 \x01(\t\x12\x10\n\x08refFiles\x18\x03 \x01(\t\x12\x0f\n\x07\x61gentID\x18\x04 \x01(\x03\"(\n&CheckAndSaveQuestionSemanticCacheReply2\xfe\x03\n\x05\x41gent\x12I\n\tCallAgent\x12\x1d.aiapi.agent.CallAgentRequest\x1a\x1b.aiapi.agent.CallAgentReply0\x01\x12S\n\rCallAgentTest\x12!.aiapi.agent.CallAgentTestRequest\x1a\x1f.aiapi.agent.CallAgentTestReply\x12V\n\x0e\x43ontractReview\x12\".aiapi.agent.ContractReviewRequest\x1a .aiapi.agent.ContractReviewReply\x12k\n\x15QuestionSecurityCheck\x12).aiapi.agent.QuestionSecurityCheckRequest\x1a\'.aiapi.agent.QuestionSecurityCheckReply\x12\x8f\x01\n!CheckAndSaveQuestionSemanticCache\x12\x35.aiapi.agent.CheckAndSaveQuestionSemanticCacheRequest\x1a\x33.aiapi.agent.CheckAndSaveQuestionSemanticCacheReplyB<Z:gitlab.minum.cloud/innovationteam/ai-api/aiapi/agent;agentb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'aiapi.agent.agent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z:gitlab.minum.cloud/innovationteam/ai-api/aiapi/agent;agent'
  _globals['_CHATPAYLOAD']._serialized_start=41
  _globals['_CHATPAYLOAD']._serialized_end=289
  _globals['_CALLAGENTREQUEST']._serialized_start=292
  _globals['_CALLAGENTREQUEST']._serialized_end=486
  _globals['_USAGE']._serialized_start=488
  _globals['_USAGE']._serialized_end=564
  _globals['_CALLAGENTREPLY']._serialized_start=567
  _globals['_CALLAGENTREPLY']._serialized_end=805
  _globals['_CALLAGENTTESTREQUEST']._serialized_start=807
  _globals['_CALLAGENTTESTREQUEST']._serialized_end=878
  _globals['_CALLAGENTTESTREPLY']._serialized_start=880
  _globals['_CALLAGENTTESTREPLY']._serialized_end=961
  _globals['_CUSTOMRULE']._serialized_start=963
  _globals['_CUSTOMRULE']._serialized_end=1015
  _globals['_CONTRACTREVIEWREQUEST']._serialized_start=1017
  _globals['_CONTRACTREVIEWREQUEST']._serialized_end=1123
  _globals['_CONTRACTREVIEWREPLY']._serialized_start=1125
  _globals['_CONTRACTREVIEWREPLY']._serialized_end=1163
  _globals['_QUESTIONSECURITYCHECKREQUEST']._serialized_start=1165
  _globals['_QUESTIONSECURITYCHECKREQUEST']._serialized_end=1231
  _globals['_QUESTIONSECURITYCHECKREPLY']._serialized_start=1233
  _globals['_QUESTIONSECURITYCHECKREPLY']._serialized_end=1274
  _globals['_CHECKANDSAVEQUESTIONSEMANTICCACHEREQUEST']._serialized_start=1276
  _globals['_CHECKANDSAVEQUESTIONSEMANTICCACHEREQUEST']._serialized_end=1387
  _globals['_CHECKANDSAVEQUESTIONSEMANTICCACHEREPLY']._serialized_start=1389
  _globals['_CHECKANDSAVEQUESTIONSEMANTICCACHEREPLY']._serialized_end=1429
  _globals['_AGENT']._serialized_start=1432
  _globals['_AGENT']._serialized_end=1942
# @@protoc_insertion_point(module_scope)
