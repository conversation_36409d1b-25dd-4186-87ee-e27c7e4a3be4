import datetime
import pytz

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

DEFAULT_DELETED_AT = datetime.datetime(1, 1, 1, 8, 0, 0, tzinfo=pytz.FixedOffset(480))  # +0800

class AtomicQuestion(Base):
    __tablename__ = 'atomic_questions'
    id = Column(BigInteger, primary_key=True)
    file_relation_id = Column(BigInteger, nullable=False)
    chunk_index = Column(BigInteger, nullable=False)
    chunk_size = Column(BigInteger, nullable=False)
    index = Column(BigInteger, nullable=False)
    question = Column(ARRAY(Text), nullable=False)
    entity_tag = Column(String(255), nullable=False)
    pre_entity_tag = Column(String(255), nullable=False)
    is_handle = Column(<PERSON>olean, nullable=False, default=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False)
    deleted_at = Column(TIMESTAMP(timezone=True), default=DEFAULT_DELETED_AT)

    @classmethod
    def add_atomic_question(cls, file_relation_id, chunk_index, chunk_size, index, question, entity_tag, pre_entity_tag, is_handle=True):
        with session_scope() as session:
            session.add(cls(
                file_relation_id=file_relation_id,
                chunk_index=chunk_index,
                chunk_size=chunk_size,
                index=index,
                question=question,
                entity_tag=entity_tag,
                pre_entity_tag=pre_entity_tag,
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow(),
                is_handle=is_handle
            ))

    @classmethod
    def get_atomic_question(cls, file_relation_id, chunk_index, chunk_size, index, entity_tag, pre_entity_tag):
        with session_scope() as session:
            return session.query(cls).filter(
                cls.file_relation_id == file_relation_id,
                cls.chunk_index == chunk_index,
                cls.chunk_size == chunk_size,
                cls.index == index,
                cls.entity_tag == entity_tag,
                cls.pre_entity_tag == pre_entity_tag
            ).first()

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}

        
    

