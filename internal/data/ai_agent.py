import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiAgent(Base):
    __tablename__ = 'ai_agent'
    id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(String(255), nullable=True)
    avatar = Column(String(255), nullable=True)
    welcome_msg = Column(String(255), nullable=True)
    fallback_msg = Column(String(255), nullable=True)
    owner_id = Column(BigInteger, nullable=False)
    visibility_type = Column(SmallInteger, nullable=False, default=0)
    knowledge_base_ids = Column(ARRAY(BigInteger), nullable=False, default=[])
    knowledge_base_type = Column(SmallInteger, nullable=False, default=0)
    schema = Column(Text, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    is_public = Column(Boolean, nullable=False, default=False)
    is_ref_files = Column(Boolean, nullable=False, default=True)
    model_type = Column(BigInteger, nullable=False, default=1)
    model_id = Column(BigInteger, nullable=False, default=1)
    internet_search = Column(Boolean, nullable=False, default=False)
    thinking_model_id = Column(BigInteger, nullable=False, default=0)
    thinking = Column(Boolean, nullable=False, default=False)
    role_setting = Column(Text, nullable=False, default="")
    semantic_cache = Column(Boolean, nullable=False, default=False)

    @classmethod
    def create_ai_agent(cls, name, description, avatar, welcome_msg, fallback_msg, owner_id, visibility_type, knowledge_base_ids, schema):
        with session_scope() as session:
            session.add(cls(
                name=name,
                description=description,
                avatar=avatar,
                welcome_msg=welcome_msg,
                fallback_msg=fallback_msg,
                owner_id=owner_id,
                visibility_type=visibility_type,
                knowledge_base_ids=knowledge_base_ids,
                schema=schema,
                created_at=datetime.datetime.utcnow()
            ))

    @classmethod
    def get_ai_agent_by_id(cls, id):
        with session_scope() as session:
            result = session.query(cls).filter(cls.id == id).first()
            if result is None:
                raise ValueError(f"AiAgent with id {id} not found")
            return result

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'avatar': self.avatar,
            'welcome_msg': self.welcome_msg,
            'fallback_msg': self.fallback_msg,
            'owner_id': self.owner_id,
            'visibility_type': self.visibility_type,
            'knowledge_base_ids': self.knowledge_base_ids,
            'schema': self.schema,
            'created_at': self.created_at,
            'is_ref_files': self.is_ref_files,
            'model_type': self.model_type,
            'model_id': self.model_id,
            'knowledge_base_type': self.knowledge_base_type,
            'internet_search': self.internet_search,
            'thinking_model_id': self.thinking_model_id,
            'thinking': self.thinking,
            'role_setting': self.role_setting,
            "semantic_cache": self.semantic_cache
        }
        
    

