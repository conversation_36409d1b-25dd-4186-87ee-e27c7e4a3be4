import unittest
from internal.data.file_content import FileContentSearch


class TestFileContentSearch(unittest.TestCase):
    def test_init(self):
        print(len(FileContentSearch.get_file_content_by_payloads([{
            "index": 1,
            "entityTag": '9b25cc1e13b3f6e22ff47db03981ad0c92c9605efdbd5fe2b86f12560674d918',
            "preEntityTag": '\\'
        },{
            "index": 0,
            "entityTag": '1805c67445b652ded91a2009d260ecc26b2cbcb3e4c87bc13f8554cce9e57dbd',
            "preEntityTag": '7fc4fb71267a27883441274f137594e0d138d2d8524e0329525b95fa8d205d3d'
        }
        ])))