from datahelper.logger import get_logger
from internal.prompts.content_sensitivity import needs_deidentification_prompt, extract_sensitivity_from_response
from config.config import config

logger = get_logger(__name__)

class SensitivityChecker:
    """
    内容敏感度检查器
    用于判断内容是否需要脱敏处理
    """
    def __init__(self, llm_client):
        self.llm_client = llm_client
        self.logger = logger
    
    def check_content_sensitivity(self, content: str) -> dict:
        """
        检查内容敏感度，判断是否需要脱敏
        
        :param content: 需要检查的内容
        :return: 包含敏感度判断的字典 {"needs_deidentification": bool}
        """
        try:
            # 构建敏感度检查提示
            prompt = needs_deidentification_prompt(content)
            
            # 调用大模型判断敏感度
            response = self.llm_client.client.chat.completions.create(
                model=config.llm_model_name,
                temperature=0,  # 使用低温度以获得更确定的结果
                messages=[
                    {
                        'role': 'system',
                        'content': '你是一位企业信息安全专家，能够准确判断内容是否包含敏感信息。'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]
            )
            
            # 提取敏感度判断结果
            content = response.choices[0].message.content
            sensitivity_result = extract_sensitivity_from_response(content)
            
            self.logger.info(f"敏感度检查结果: 需要脱敏={sensitivity_result['needs_deidentification']}")
            
            return sensitivity_result
        except Exception as e:
            self.logger.error(f"敏感度检查出错: {e}")
            # 出错时返回保守的默认值（需要脱敏）
            return {
                "needs_deidentification": True
            } 