syntax = "proto3";

package aiapi.search;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-api/aiapi/search;search";

message FullTextSearchRequest {
  string query = 1;
  int64 searchType = 2;
  string fileType = 3;
  repeated int64 ownerIDs = 4;
  google.protobuf.Timestamp startTime = 5;
  google.protobuf.Timestamp endTime = 6;
  int64 pageNum = 9;
  int64 pageSize = 10;
  string classPath = 11;
  string path = 12;
  bool filterSameFile = 13;
}

message Documents {
  string text = 1;
  string entityTag = 2;
  string preEntityTag = 3;
  int64 fileRelationID = 4;
  google.protobuf.Timestamp updatedAt = 5;
  string title = 6;
  int64 userID = 7;
  string userName = 8;
  string fullPath = 9;
  repeated string tagNames = 10;
  int64 size = 11;
  string mimeType = 12;
  bool canDoAiProcess = 13;
}

message FullTextSearchReply {
  string query = 1;
  repeated string tsQuery = 6;
  int64 pageNum = 2;
  int64 pageSize = 3;
  repeated Documents refs = 4;
  int64 total = 5;
}

enum QAStatus {
  RUNNING = 0;
  STOPPED = 1;
}

enum QAReplyType {
  CONTENT = 0;
  ABSTRACT_PROCESS = 1;
  TOO_MANY_REQUESTS = 2;
}

message QASearchRequest {
  string query = 1;
  int64 roundID = 2;
  repeated int64 fileRelationIDs = 3;
}

message QASearchReply {
  int64 roundID = 1;
  string content = 2;
  int64 status = 3;
  repeated ChatPayload payloads = 4;
  int64 type = 5;
}

message ChatHistoryRequest {
  int64 pageSize = 1;
  int64 pageNum = 2;
}

message ChatHistory {
  int64 userID = 1;
  int64 tenantID = 2;
  int64 roundID = 3;
  string query = 4;
  string answer = 5;
  repeated ChatPayload payloads = 6;
  google.protobuf.Timestamp createdAt = 7;
}

message ChatPayload {
  int64 fileRelationID = 1;
  string entityTag = 2;
  string preEntityTag = 3;
  string name = 4;
  int64 size = 5;
  int64 userID = 6;
  string mimeType = 7;
  int64 index = 8;
  int64 chunkIndex = 9;
  int64 chunkSize = 10;
}

message ChatHistoryReply {
  repeated ChatHistory histories = 1;
}

message KnowledgeBaseSearchRequest {
  string query = 1;
  repeated int64 knowledbeBaseIDs = 2;
  int64 maxNum = 3;
  float rerankThreshold = 4;
}

message KnowledgeBaseSearchReply {
  repeated string contents = 1;
}

service Search {
  rpc FullTextSearch(FullTextSearchRequest) returns (FullTextSearchReply);
  rpc QASearch(QASearchRequest) returns (stream QASearchReply);
  rpc ChatHistory(ChatHistoryRequest) returns (ChatHistoryReply);
  rpc KnowledgeBaseSearch(KnowledgeBaseSearchRequest) returns (KnowledgeBaseSearchReply);
  rpc DoChunks(DoChunksRequest) returns (DoChunksReply);
}

message DoChunksRequest {
  string content = 1;
  bool advancedProcess = 2;
}

message DoChunksReply {
  repeated Chunk chunks = 1;
}

message Chunk {
  string content = 1;
  repeated string images = 2;
}
