# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: aiapi/classify/classify.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'aiapi/classify/classify.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1d\x61iapi/classify/classify.proto\x12\x0e\x61iapi.classify\"o\n\x0f\x43lassifyRequest\x12\x11\n\tentityTag\x18\x01 \x01(\t\x12\x14\n\x0cpreEntityTag\x18\x02 \x01(\t\x12\x16\n\x0e\x66ileRelationID\x18\x03 \x01(\x03\x12\x1b\n\x13\x65xisting_categories\x18\x04 \x03(\t\"-\n\x10\x43lassifyResponse\x12\x19\n\x11suggestedCategory\x18\x01 \x01(\t2b\n\x0f\x43lassifyService\x12O\n\nDoClassify\x12\x1f.aiapi.classify.ClassifyRequest\x1a .<EMAIL>/innovationteam/ai-api/aiapi/classify;classifyb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'aiapi.classify.classify_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'<EMAIL>/innovationteam/ai-api/aiapi/classify;classify'
  _globals['_CLASSIFYREQUEST']._serialized_start=49
  _globals['_CLASSIFYREQUEST']._serialized_end=160
  _globals['_CLASSIFYRESPONSE']._serialized_start=162
  _globals['_CLASSIFYRESPONSE']._serialized_end=207
  _globals['_CLASSIFYSERVICE']._serialized_start=209
  _globals['_CLASSIFYSERVICE']._serialized_end=307
# @@protoc_insertion_point(module_scope)
