// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: search/search.proto

package search

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FullTextSearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullTextSearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullTextSearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullTextSearchRequestMultiError, or nil if none found.
func (m *FullTextSearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FullTextSearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for SearchType

	// no validation rules for FileType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullTextSearchRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullTextSearchRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageNum

	// no validation rules for PageSize

	// no validation rules for ClassPath

	// no validation rules for Path

	// no validation rules for FilterSameFile

	if len(errors) > 0 {
		return FullTextSearchRequestMultiError(errors)
	}

	return nil
}

// FullTextSearchRequestMultiError is an error wrapping multiple validation
// errors returned by FullTextSearchRequest.ValidateAll() if the designated
// constraints aren't met.
type FullTextSearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullTextSearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullTextSearchRequestMultiError) AllErrors() []error { return m }

// FullTextSearchRequestValidationError is the validation error returned by
// FullTextSearchRequest.Validate if the designated constraints aren't met.
type FullTextSearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullTextSearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullTextSearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullTextSearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullTextSearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullTextSearchRequestValidationError) ErrorName() string {
	return "FullTextSearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FullTextSearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullTextSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullTextSearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullTextSearchRequestValidationError{}

// Validate checks the field values on Documents with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Documents) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Documents with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DocumentsMultiError, or nil
// if none found.
func (m *Documents) ValidateAll() error {
	return m.validate(true)
}

func (m *Documents) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for FileRelationID

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DocumentsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Title

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for FullPath

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for CanDoAiProcess

	if len(errors) > 0 {
		return DocumentsMultiError(errors)
	}

	return nil
}

// DocumentsMultiError is an error wrapping multiple validation errors returned
// by Documents.ValidateAll() if the designated constraints aren't met.
type DocumentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DocumentsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DocumentsMultiError) AllErrors() []error { return m }

// DocumentsValidationError is the validation error returned by
// Documents.Validate if the designated constraints aren't met.
type DocumentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DocumentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DocumentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DocumentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DocumentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DocumentsValidationError) ErrorName() string { return "DocumentsValidationError" }

// Error satisfies the builtin error interface
func (e DocumentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDocuments.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DocumentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DocumentsValidationError{}

// Validate checks the field values on FullTextSearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullTextSearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullTextSearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullTextSearchReplyMultiError, or nil if none found.
func (m *FullTextSearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FullTextSearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for PageNum

	// no validation rules for PageSize

	for idx, item := range m.GetRefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FullTextSearchReplyValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FullTextSearchReplyValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FullTextSearchReplyValidationError{
					field:  fmt.Sprintf("Refs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return FullTextSearchReplyMultiError(errors)
	}

	return nil
}

// FullTextSearchReplyMultiError is an error wrapping multiple validation
// errors returned by FullTextSearchReply.ValidateAll() if the designated
// constraints aren't met.
type FullTextSearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullTextSearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullTextSearchReplyMultiError) AllErrors() []error { return m }

// FullTextSearchReplyValidationError is the validation error returned by
// FullTextSearchReply.Validate if the designated constraints aren't met.
type FullTextSearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullTextSearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullTextSearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullTextSearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullTextSearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullTextSearchReplyValidationError) ErrorName() string {
	return "FullTextSearchReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FullTextSearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullTextSearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullTextSearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullTextSearchReplyValidationError{}

// Validate checks the field values on QASearchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QASearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QASearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QASearchRequestMultiError, or nil if none found.
func (m *QASearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QASearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for RoundID

	if len(errors) > 0 {
		return QASearchRequestMultiError(errors)
	}

	return nil
}

// QASearchRequestMultiError is an error wrapping multiple validation errors
// returned by QASearchRequest.ValidateAll() if the designated constraints
// aren't met.
type QASearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QASearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QASearchRequestMultiError) AllErrors() []error { return m }

// QASearchRequestValidationError is the validation error returned by
// QASearchRequest.Validate if the designated constraints aren't met.
type QASearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QASearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QASearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QASearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QASearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QASearchRequestValidationError) ErrorName() string { return "QASearchRequestValidationError" }

// Error satisfies the builtin error interface
func (e QASearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQASearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QASearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QASearchRequestValidationError{}

// Validate checks the field values on QASearchReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QASearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QASearchReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QASearchReplyMultiError, or
// nil if none found.
func (m *QASearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QASearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RoundID

	// no validation rules for Content

	// no validation rules for Status

	for idx, item := range m.GetPayloads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QASearchReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QASearchReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QASearchReplyValidationError{
					field:  fmt.Sprintf("Payloads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	if len(errors) > 0 {
		return QASearchReplyMultiError(errors)
	}

	return nil
}

// QASearchReplyMultiError is an error wrapping multiple validation errors
// returned by QASearchReply.ValidateAll() if the designated constraints
// aren't met.
type QASearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QASearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QASearchReplyMultiError) AllErrors() []error { return m }

// QASearchReplyValidationError is the validation error returned by
// QASearchReply.Validate if the designated constraints aren't met.
type QASearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QASearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QASearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QASearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QASearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QASearchReplyValidationError) ErrorName() string { return "QASearchReplyValidationError" }

// Error satisfies the builtin error interface
func (e QASearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQASearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QASearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QASearchReplyValidationError{}

// Validate checks the field values on ChatHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChatHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatHistoryRequestMultiError, or nil if none found.
func (m *ChatHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageSize

	// no validation rules for PageNum

	if len(errors) > 0 {
		return ChatHistoryRequestMultiError(errors)
	}

	return nil
}

// ChatHistoryRequestMultiError is an error wrapping multiple validation errors
// returned by ChatHistoryRequest.ValidateAll() if the designated constraints
// aren't met.
type ChatHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatHistoryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatHistoryRequestMultiError) AllErrors() []error { return m }

// ChatHistoryRequestValidationError is the validation error returned by
// ChatHistoryRequest.Validate if the designated constraints aren't met.
type ChatHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatHistoryRequestValidationError) ErrorName() string {
	return "ChatHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChatHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatHistoryRequestValidationError{}

// Validate checks the field values on ChatHistory with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChatHistory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatHistory with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChatHistoryMultiError, or
// nil if none found.
func (m *ChatHistory) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatHistory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserID

	// no validation rules for TenantID

	// no validation rules for RoundID

	// no validation rules for Query

	// no validation rules for Answer

	for idx, item := range m.GetPayloads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatHistoryValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatHistoryValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatHistoryValidationError{
					field:  fmt.Sprintf("Payloads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChatHistoryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChatHistoryValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChatHistoryValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChatHistoryMultiError(errors)
	}

	return nil
}

// ChatHistoryMultiError is an error wrapping multiple validation errors
// returned by ChatHistory.ValidateAll() if the designated constraints aren't met.
type ChatHistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatHistoryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatHistoryMultiError) AllErrors() []error { return m }

// ChatHistoryValidationError is the validation error returned by
// ChatHistory.Validate if the designated constraints aren't met.
type ChatHistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatHistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatHistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatHistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatHistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatHistoryValidationError) ErrorName() string { return "ChatHistoryValidationError" }

// Error satisfies the builtin error interface
func (e ChatHistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatHistory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatHistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatHistoryValidationError{}

// Validate checks the field values on ChatPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChatPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChatPayloadMultiError, or
// nil if none found.
func (m *ChatPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for UserID

	// no validation rules for MimeType

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for ChunkSize

	if len(errors) > 0 {
		return ChatPayloadMultiError(errors)
	}

	return nil
}

// ChatPayloadMultiError is an error wrapping multiple validation errors
// returned by ChatPayload.ValidateAll() if the designated constraints aren't met.
type ChatPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatPayloadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatPayloadMultiError) AllErrors() []error { return m }

// ChatPayloadValidationError is the validation error returned by
// ChatPayload.Validate if the designated constraints aren't met.
type ChatPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatPayloadValidationError) ErrorName() string { return "ChatPayloadValidationError" }

// Error satisfies the builtin error interface
func (e ChatPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatPayloadValidationError{}

// Validate checks the field values on ChatHistoryReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChatHistoryReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatHistoryReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatHistoryReplyMultiError, or nil if none found.
func (m *ChatHistoryReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatHistoryReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHistories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatHistoryReplyValidationError{
						field:  fmt.Sprintf("Histories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatHistoryReplyValidationError{
						field:  fmt.Sprintf("Histories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatHistoryReplyValidationError{
					field:  fmt.Sprintf("Histories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ChatHistoryReplyMultiError(errors)
	}

	return nil
}

// ChatHistoryReplyMultiError is an error wrapping multiple validation errors
// returned by ChatHistoryReply.ValidateAll() if the designated constraints
// aren't met.
type ChatHistoryReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatHistoryReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatHistoryReplyMultiError) AllErrors() []error { return m }

// ChatHistoryReplyValidationError is the validation error returned by
// ChatHistoryReply.Validate if the designated constraints aren't met.
type ChatHistoryReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatHistoryReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatHistoryReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatHistoryReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatHistoryReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatHistoryReplyValidationError) ErrorName() string { return "ChatHistoryReplyValidationError" }

// Error satisfies the builtin error interface
func (e ChatHistoryReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatHistoryReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatHistoryReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatHistoryReplyValidationError{}

// Validate checks the field values on KnowledgeBaseSearchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeBaseSearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeBaseSearchRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeBaseSearchRequestMultiError, or nil if none found.
func (m *KnowledgeBaseSearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeBaseSearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for MaxNum

	// no validation rules for RerankThreshold

	if len(errors) > 0 {
		return KnowledgeBaseSearchRequestMultiError(errors)
	}

	return nil
}

// KnowledgeBaseSearchRequestMultiError is an error wrapping multiple
// validation errors returned by KnowledgeBaseSearchRequest.ValidateAll() if
// the designated constraints aren't met.
type KnowledgeBaseSearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeBaseSearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeBaseSearchRequestMultiError) AllErrors() []error { return m }

// KnowledgeBaseSearchRequestValidationError is the validation error returned
// by KnowledgeBaseSearchRequest.Validate if the designated constraints aren't met.
type KnowledgeBaseSearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeBaseSearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeBaseSearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeBaseSearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeBaseSearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeBaseSearchRequestValidationError) ErrorName() string {
	return "KnowledgeBaseSearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeBaseSearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeBaseSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeBaseSearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeBaseSearchRequestValidationError{}

// Validate checks the field values on KnowledgeBaseSearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeBaseSearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeBaseSearchReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeBaseSearchReplyMultiError, or nil if none found.
func (m *KnowledgeBaseSearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeBaseSearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return KnowledgeBaseSearchReplyMultiError(errors)
	}

	return nil
}

// KnowledgeBaseSearchReplyMultiError is an error wrapping multiple validation
// errors returned by KnowledgeBaseSearchReply.ValidateAll() if the designated
// constraints aren't met.
type KnowledgeBaseSearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeBaseSearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeBaseSearchReplyMultiError) AllErrors() []error { return m }

// KnowledgeBaseSearchReplyValidationError is the validation error returned by
// KnowledgeBaseSearchReply.Validate if the designated constraints aren't met.
type KnowledgeBaseSearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeBaseSearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeBaseSearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeBaseSearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeBaseSearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeBaseSearchReplyValidationError) ErrorName() string {
	return "KnowledgeBaseSearchReplyValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeBaseSearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeBaseSearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeBaseSearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeBaseSearchReplyValidationError{}

// Validate checks the field values on DoChunksRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DoChunksRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DoChunksRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DoChunksRequestMultiError, or nil if none found.
func (m *DoChunksRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DoChunksRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for AdvancedProcess

	if len(errors) > 0 {
		return DoChunksRequestMultiError(errors)
	}

	return nil
}

// DoChunksRequestMultiError is an error wrapping multiple validation errors
// returned by DoChunksRequest.ValidateAll() if the designated constraints
// aren't met.
type DoChunksRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DoChunksRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DoChunksRequestMultiError) AllErrors() []error { return m }

// DoChunksRequestValidationError is the validation error returned by
// DoChunksRequest.Validate if the designated constraints aren't met.
type DoChunksRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DoChunksRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DoChunksRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DoChunksRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DoChunksRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DoChunksRequestValidationError) ErrorName() string { return "DoChunksRequestValidationError" }

// Error satisfies the builtin error interface
func (e DoChunksRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDoChunksRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DoChunksRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DoChunksRequestValidationError{}

// Validate checks the field values on DoChunksReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DoChunksReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DoChunksReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DoChunksReplyMultiError, or
// nil if none found.
func (m *DoChunksReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DoChunksReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChunks() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DoChunksReplyValidationError{
						field:  fmt.Sprintf("Chunks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DoChunksReplyValidationError{
						field:  fmt.Sprintf("Chunks[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DoChunksReplyValidationError{
					field:  fmt.Sprintf("Chunks[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DoChunksReplyMultiError(errors)
	}

	return nil
}

// DoChunksReplyMultiError is an error wrapping multiple validation errors
// returned by DoChunksReply.ValidateAll() if the designated constraints
// aren't met.
type DoChunksReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DoChunksReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DoChunksReplyMultiError) AllErrors() []error { return m }

// DoChunksReplyValidationError is the validation error returned by
// DoChunksReply.Validate if the designated constraints aren't met.
type DoChunksReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DoChunksReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DoChunksReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DoChunksReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DoChunksReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DoChunksReplyValidationError) ErrorName() string { return "DoChunksReplyValidationError" }

// Error satisfies the builtin error interface
func (e DoChunksReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDoChunksReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DoChunksReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DoChunksReplyValidationError{}

// Validate checks the field values on Chunk with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Chunk) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Chunk with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ChunkMultiError, or nil if none found.
func (m *Chunk) ValidateAll() error {
	return m.validate(true)
}

func (m *Chunk) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	if len(errors) > 0 {
		return ChunkMultiError(errors)
	}

	return nil
}

// ChunkMultiError is an error wrapping multiple validation errors returned by
// Chunk.ValidateAll() if the designated constraints aren't met.
type ChunkMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChunkMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChunkMultiError) AllErrors() []error { return m }

// ChunkValidationError is the validation error returned by Chunk.Validate if
// the designated constraints aren't met.
type ChunkValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChunkValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChunkValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChunkValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChunkValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChunkValidationError) ErrorName() string { return "ChunkValidationError" }

// Error satisfies the builtin error interface
func (e ChunkValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChunk.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChunkValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChunkValidationError{}
