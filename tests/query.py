import asyncio
import unittest
import json
from grpclib.client import Channel
from aiapi.search.search_pb2 import QASearchRequest, QAReplyType
from aiapi.search.search_grpc import SearchStub

session = {
    "UserID": 3,
    "TenantID": 1
}


async def _query():
    async with Channel("localhost", 16006) as channel:
        stub = SearchStub(channel)
        async with stub.QASearch.open(
                metadata={'x-md-global-authz-session': json.dumps(session)}) as stream:
            await stream.send_message(QASearchRequest(query="根据新员工考核指南，分别根据周报内容，帮我生成最终达标员工",
                                                      fileRelationIDs=[3351,3353,3355,3358,3360,3357]), end=True)
            content = ""
            async for reply in stream:
                # if not get_payloads:
                #     json_body = json.loads(MessageToJson(reply))
                #     payloads = json_body.get('payloads')
                #     get_payloads = True
                content += reply.content
            print(content)


class TestQuery(unittest.TestCase):
    def test_query(self):
        asyncio.run(_query())
