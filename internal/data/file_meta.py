import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import VARCHAR, Boolean, Column, BigInteger, TEXT, TIMESTAMP

from internal.data.db import session_scope, engine
from config.config import config

Base = declarative_base()


class FileMeta(Base):
    __tablename__ = 'file_meta'
    id = Column(BigInteger, primary_key=True)
    file_relation_id = Column(BigInteger)
    user_id = Column(BigInteger)
    entity_tag = Column(VARCHAR(64))
    pre_entity_tag = Column(VARCHAR(64))
    name = Column(VARCHAR(255))
    size = Column(BigInteger)
    mime_type = Column(VARCHAR(32))
    full_path = Column(VARCHAR(255))
    can_do_ai_process = Column(Boolean)

    @classmethod
    def get_file_meta(cls, file_relation_id, entity_tag, pre_entity_tag):
        with session_scope() as session:
            return session.query(cls).filter(cls.file_relation_id == file_relation_id, cls.entity_tag == entity_tag, cls.pre_entity_tag == pre_entity_tag).first()
        
    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}
