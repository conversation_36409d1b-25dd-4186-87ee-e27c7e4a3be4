# coding:utf-8
import asyncio
import threading

import grpc
from middleware.metrics import PrometheusServerInterceptor
from prometheus_client import start_http_server
from flask import Flask, jsonify
from flask_cors import CORS

from aiapi.agent import agent_pb2_grpc
from aiapi.health import health_pb2_grpc
from aiapi.search import search_pb2_grpc
from config.config import config
from datahelper.logger import get_logger
from internal.service.agent import Agent
from internal.kafka_consumer.handle_chunk import ChunkHandler
from internal.kafka_consumer.handle_chunk_immediately import ChunkImmediatelyHandler
from internal.queue_consumer.message_consumer import start_consumer
from internal.service.health import Health
from internal.service.search import Search
from routes.api import api_bp

logger = get_logger(__name__)

# 创建Flask应用
app = Flask(__name__)
# 启用CORS
CORS(app, resources={r"/*": {"origins": "*"}})

# 注册蓝图
app.register_blueprint(api_bp, url_prefix='/api')

# 定义根路由
@app.route('/')
def index():
    return jsonify({"status": "ok", "service": "ai-api-http"})

# 启动Flask服务的函数
def start_flask_server(host='0.0.0.0', port=15008):
    # 注册Flask服务到Nacos
    try:
        config.client.add_naming_instance("ai-api.http", config.local_ip, port,
                                          cluster_name="DEFAULT", weight=1,
                                          metadata='{"version":"v1.0", "kind":"http"}', enable=True,
                                          healthy=True)
        # 定期发送心跳
        def send_heartbeat():
            while True:
                try:
                    config.client.send_heartbeat("ai-api.http", config.local_ip, port, "DEFAULT", 1)
                except Exception as e:
                    logger.warning(f"HTTP服务心跳发送失败: {e}")
                finally:
                    # 每5秒发送一次心跳
                    threading.Event().wait(5)
        
        # 启动心跳线程
        heartbeat_thread = threading.Thread(target=send_heartbeat, daemon=True)
        heartbeat_thread.start()
        
        logger.info(f"Flask HTTP服务已注册到Nacos: {config.local_ip}:{port}")
    except Exception as e:
        logger.error(f"Flask服务注册到Nacos失败: {e}")
    
    # 启动Flask服务
    app.run(host=host, port=port, debug=False, use_reloader=False, threaded=True)


async def main(*, port=config.port):
    server = grpc.aio.server(interceptors=(PrometheusServerInterceptor(),))

    health_pb2_grpc.add_HealthServicer_to_server(Health(), server)
    agent_pb2_grpc.add_AgentServicer_to_server(Agent(), server)
    search_pb2_grpc.add_SearchServicer_to_server(Search(), server)

    start_http_server(port + 1)
    server.add_insecure_port(f"[::]:{port}")

    # 启动Kafka消费者
    chunk_handler = ChunkHandler()
    chunk_handler.start()
    logger.info("Kafka consumer thread started")
    chunk_immediately_handler = ChunkImmediatelyHandler()
    chunk_immediately_handler.start()
    logger.info("Kafka consumer thread started")

    # 启动消息队列消费者
    message_consumer = start_consumer()
    logger.info("Message queue consumer thread started")
    
    # 在单独的线程中启动Flask HTTP服务
    flask_thread = threading.Thread(target=start_flask_server, daemon=True)
    flask_thread.start()
    logger.info("Flask HTTP server thread started on port 5000")

    await server.start()
    logger.info(f"Server started on port {port}")
    await server.wait_for_termination()


if __name__ == "__main__":
    asyncio.run(main())
