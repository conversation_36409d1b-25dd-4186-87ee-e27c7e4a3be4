import time

import redis
from config.config import config
from typing import Union


class TooManyRequests(Exception):
    pass


class Cache:
    def __init__(self, model_type: int):
        pool = redis.ConnectionPool.from_url("redis://:{}@{}?decode_responses=True".
                                             format(config.redis_password, config.redis_host))
        self.client = redis.Redis(connection_pool=pool)
        if model_type == 1:
            self.llm_chat_limit = config.llm_chat_limit
            self.key = config.limit_key
            self.normal_user_chat_limit_template = "normal_user_chat_limit_{}"
        elif model_type == 2:
            self.llm_chat_limit = config.external_llm_chat_limit
            self.key = config.public_limit_key
            self.normal_user_chat_limit_template = "normal_user_public_chat_limit_{}"

    def is_exceeded(self, request_id, user_id=0) -> Union[TooManyRequests, None]:
        # 删除过期数据(10 分钟以上的数据)
        self.client.zremrangebyscore(self.key, 0, request_id - 6e11)

        # 普通用户先检查一分钟内是否有被限流过
        if user_id > 0:
            key = self.normal_user_chat_limit_template.format(user_id)
            if self.client.exists(key) == 1:
                self.client.expire(key, 60)
                raise TooManyRequests
        # 尝试获取配额, 超额则限流, 普通用户超额还需要标记一分钟内不准再次访问
        count = self.client.zcard(self.key)
        if count < self.llm_chat_limit:
            self.client.zadd(self.key, {request_id: request_id})
            return
        else:
            if user_id > 0:
                self.client.set(self.normal_user_chat_limit_template.format(user_id), "", 60)
            raise TooManyRequests

    def try_release(self, request_id):
        self.client.zrem(self.key, request_id)

    def get_white_list(self):
        return self.client.smembers("global_agent_white_list") or []