from typing import Dict, Any
import grpc

from .base_handler import <PERSON>deHandler, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow

class ConditionNodeHandler(NodeHandler):
    """条件节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理条件节点"""
        # 获取条件配置
        node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
        inputs_data = node_data.get("inputs", {})
        branches = inputs_data.get("branches", [])
        
        if not branches:
            state["condition_result"] = "true"
            return state
            
        branch = branches[0]  # 获取第一个分支的条件
        conditions = branch["condition"]["conditions"]
        
        # 评估条件
        for condition in conditions:
            # 获取左值
            left_value = self._get_condition_value(condition["left"], state)
            # 获取右值
            right_value = self._get_condition_value(condition["right"], state)

            # 根据操作符比较值的长度
            if self._compare_values(left_value, condition["operator"], right_value):
                state["condition_result"] = "true"
                return state
        
        state["condition_result"] = "false"
        return state

    def _get_condition_value(self, value_dict: Dict[str, Any], state: WorkflowState) -> Any:
        """从条件配置中获取实际值"""
        for _, input_value in value_dict.items():
            if input_value["value"]["type"] == "ref":
                # 从其他节点获取值
                source_node = input_value["value"]["content"]["blockID"]
                output_name = input_value["value"]["content"]["name"]
                return state["node_outputs"][source_node][output_name]["value"]
            else:
                # 返回字面量
                return input_value["value"]["content"]

    def _compare_values(self, left: Any, operator: int, right: Any) -> bool:
        """比较两个值"""
        if operator == 1:  # 等于
            return left == right
        elif operator == 2:  # 不等于
            return left != right
        elif operator == 3:  # 长度大于
            return len(left) > int(right)
        elif operator == 4:  # 长度小于
            return len(left) < int(right)
        elif operator == 5:  # 长度大于等于
            return len(left) >= int(right)
        elif operator == 6:  # 长度小于等于
            return len(left) <= int(right)
        elif operator == 7:  # 长度等于
            return len(left) == int(right)
        return False 