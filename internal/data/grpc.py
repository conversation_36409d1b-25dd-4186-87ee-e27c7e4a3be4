from typing import List

import grpc
from dataembedding.embed import embed_pb2_grpc
from dataembedding.embed.embed_pb2 import EmbeddingReply, EmbeddingRequest
from dataembedding.rerank import rerank_pb2_grpc
from dataembedding.rerank.rerank_pb2 import (RerankReply, RerankRequest,
                                             SentencePair)
from datahelper.logger import get_logger

from config.config import config

logger = get_logger(__name__)


class EmbeddingClient:
    def __init__(self):
        address = config.get_naming_instance("data-embedding.grpc")
        channel = grpc.insecure_channel(address)
        ready_future = grpc.channel_ready_future(channel)
        try:
            ready_future.result(timeout=5)
        except:
            logger.info(f"grpc client connect to data-embedding at {address} timeout")
            exit(1)
        logger.info(f"grpc client connected to data-embedding at {address}")
        self.embedding_stub = embed_pb2_grpc.EmbeddingStub(channel)
        self.rerank_stub = rerank_pb2_grpc.RerankStub(channel)
        self.config = config

    def embedding(self, texts: List[str]) -> EmbeddingReply:
        return self.embedding_stub.Embedding(EmbeddingRequest(sentences=texts))

    def rerank(self, texts: List[SentencePair]) -> RerankReply:
        return self.rerank_stub.Rerank(RerankRequest(pairs=texts))


embedding_client = EmbeddingClient()
