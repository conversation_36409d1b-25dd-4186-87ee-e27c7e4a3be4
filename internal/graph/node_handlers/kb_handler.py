import grpc
from typing import Dict, Any, List

from .base_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, WorkflowState
from internal.data.data import DataRepo
from internal.graph.workflow import Node, Workflow
from internal.utils.convert import to_chunk_payloads_with_context_for_agent, to_chunk_payloads_for_agent
from aiapi.agent.agent_pb2 import CallAgentReply

class KBNodeHandler(NodeHandler):
    """知识库检索节点处理器"""
    
    def __init__(self, node: Node, workflow: Workflow, context: grpc.aio.ServicerContext, repo: DataRepo):
        super().__init__(node, workflow, context, repo)
    
    async def handle(self, state: WorkflowState) -> WorkflowState:
        """处理知识库检索节点"""

        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent="检索知识库节点开始..."))

        node_data = self.node.data if isinstance(self.node.data, dict) else self.node.data.__dict__
        
        # 获取查询参数
        inputs_data = node_data.get("inputs", {})
        input_params = inputs_data.get("inputParameters", [])
        
        # 构建查询
        query = ""
        for param in input_params:
            if param["input"]["value"]["type"] == "ref":
                source_node = param["input"]["value"]["content"]["blockID"]
                output_name = param["input"]["value"]["content"]["name"]
                query = state["node_outputs"][source_node][output_name]["value"]

        # 这里是实际的知识库检索逻辑
        context = "这里是从知识库检索到的相关内容...\n"

        agent_id = state["agent_id"]
        ai_agent = self.repo.ai_agent_client.get_ai_agent_by_id(agent_id).to_dict()
        knowledge_base_ids = ai_agent["knowledge_base_ids"]

        user_ids = []
        if ai_agent["knowledge_base_type"] == 1:
            user_ids = state["user_ids"]

        results = self.repo.knowledge_base_search(query, knowledge_base_ids, user_ids)
        state["payloads"] = to_chunk_payloads_with_context_for_agent(results)

        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent=f"查询知识库：{knowledge_base_ids}"))

        if not state["test_mode"]:
            await self.context.write(CallAgentReply(type=0, debugContent=f"查询知识库结果：{to_chunk_payloads_with_context_for_agent(results)}"))
        
        if results is not None and len(results) > 0:
            if not state["test_mode"]:
                if ai_agent["is_ref_files"]:
                    for res in results:
                        await self.context.write(CallAgentReply(roundID=1, content="", status=1, payloads=to_chunk_payloads_for_agent([res]), type=0))
            count = 1
            for res in results:
                name = res.get("name")
                content = res.get("content")
                context += f"{count}. <<<文件名: {name}>>> <<<文件内容: {content}>>> \n"
                count += 1
        
        # 保存检索结果
        state["node_outputs"][self.node.id] = {
            "output": {
                "value": context,
                "type": "string"
            }
        }
        
        return state 