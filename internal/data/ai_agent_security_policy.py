import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiAgentSecurityPolicy(Base):
    __tablename__ = 'ai_agent_security_policy'
    id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False)
    policy_category = Column(BigInteger, nullable=False)
    risk_level = Column(BigInteger, nullable=False)
    enabled = Column(Boolean, nullable=False)
    policies = Column(ARRAY(Text), nullable=False)
    hit_action = Column(BigInteger, nullable=False)
    hit_response = Column(Text, nullable=False)
    agent_id = Column(BigInteger, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    deleted_at = Column(TIMESTAMP(timezone=True), nullable=True)
    
    def to_dict(self) -> dict:
        """
        将对象转换为字典
        
        Returns:
            dict: 包含对象所有属性的字典
        """
        return {
            'id': self.id,
            'name': self.name,
            'policy_category': self.policy_category,
            'risk_level': self.risk_level,
            'enabled': self.enabled,
            'policies': self.policies,
            'hit_action': self.hit_action,
            'hit_response': self.hit_response,
            'agent_id': self.agent_id,
            'created_at': self.created_at,
            'deleted_at': self.deleted_at
        }
    
    @staticmethod
    def get_policies_by_agent_id(agent_id: int) -> list:
        """
        根据agent_id获取policy列表
        
        Args:
            agent_id: 代理ID
            
        Returns:
            list: policy列表
        """
        with session_scope() as session:
            policies = session.query(AiAgentSecurityPolicy).filter(
                AiAgentSecurityPolicy.agent_id == agent_id,
                AiAgentSecurityPolicy.deleted_at == datetime.datetime(1, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
            ).all()
            return [policy.to_dict() for policy in policies]

