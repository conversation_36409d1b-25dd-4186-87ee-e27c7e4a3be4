def needs_deidentification_prompt(content: str) -> str:
    """
    创建提示模板，用于判断内容是否需要脱敏
    
    :param content: 需要判断的内容
    :return: 用于大模型的提示模板
    """
    return f"""
    你是一位企业信息安全专家，需要判断用户问题是否涉及敏感信息，需要进行脱敏处理。

---Goal---

判断用户的问题是否属于安全内容，还是需要进行脱敏处理的敏感内容。

---Instructions---

评估用户问题内容，判断是否符合以下安全内容标准：

• 安全内容标准（不需要脱敏）：
  1. 通用性方法论：涉及流程优化、策略制定、技术应用方向等，无具体数据或内部指标
  2. 市场公开信息：如竞品定价、市场需求趋势等，数据可通过公开渠道获取
  3. 合规框架：仅涉及合规要求或法规应对方向，不包含内部合规结果或整改细节
  4. 生活常识
  5. 行业常识
  6. 公开数据
  7. 基于行业普遍经验的优化建议，不涉及内部销售数据或策略细节
  8. 未提及具体技术路线或内部数据
  9. 未涉及核心技术参数或专利信息
  10. 不会暴露企业内部管理漏洞
  11. 不会暴露企业内部风险

• 敏感内容标准（需要脱敏）：
  1. 涉及内部流程细节
  2. 涉及核心技术
  3. 包含敏感企业数据
  4. 包含企业内部数据
  5. 包含内部指标
  6. 包含内部销售数据或策略细节
  7. 涉及具体技术路线
  8. 涉及核心技术参数或专利信息
  9. 可能暴露企业内部管理漏洞
  10. 可能暴露企业内部风险

• 输出格式为简单的JSON：
  {{
    "needs_deidentification": true/false  // 是否需要脱敏，只需提供布尔值
  }}

---Examples---

Example 1:
用户问题：如何优化产品的市场推广策略？
Output:
{{
    "needs_deidentification": false
}}

Example 2:
用户问题：我们公司Q1季度的销售额是多少？可以分析下原因吗？
Output:
{{
    "needs_deidentification": true
}}

Example 3:
用户问题：行业中常用的数据加密方法有哪些？哪种更安全？
Output:
{{
    "needs_deidentification": false
}}

Example 4:
用户问题：我们系统中的用户数据存储结构是怎样的？可以看看有什么安全漏洞吗？
Output:
{{
    "needs_deidentification": true
}}

Example 5:
用户问题：晚饭吃什么好？推荐几个健康的选择。
Output:
{{
    "needs_deidentification": false
}}

---Real Data---

用户问题：
{content}

######################
Output:
    """

def extract_sensitivity_from_response(response: str) -> dict:
    """
    从大模型返回的响应中提取敏感度判断结果
    
    :param response: 大模型的原始响应
    :return: 包含敏感度判断的字典
    """
    import json
    import re
    
    # 尝试找到JSON格式的内容
    try:
        # 清理响应中的非JSON内容
        json_match = re.search(r'(\{.*\})', response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            # 解析JSON
            result = json.loads(json_str)
            
            # 验证返回的字段
            if 'needs_deidentification' in result:
                return {"needs_deidentification": result['needs_deidentification']}
            else:
                raise ValueError("返回的JSON缺少必要字段")
        else:
            raise ValueError("无法从响应中提取JSON内容")
    except Exception as e:
        # 如果解析失败，返回保守的默认值（需要脱敏）
        return {
            "needs_deidentification": True
        } 