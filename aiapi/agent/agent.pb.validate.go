// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/agent.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ChatPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChatPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChatPayloadMultiError, or
// nil if none found.
func (m *ChatPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for UserID

	// no validation rules for MimeType

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for ChunkSize

	// no validation rules for Content

	// no validation rules for FullPath

	if len(errors) > 0 {
		return ChatPayloadMultiError(errors)
	}

	return nil
}

// ChatPayloadMultiError is an error wrapping multiple validation errors
// returned by ChatPayload.ValidateAll() if the designated constraints aren't met.
type ChatPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatPayloadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatPayloadMultiError) AllErrors() []error { return m }

// ChatPayloadValidationError is the validation error returned by
// ChatPayload.Validate if the designated constraints aren't met.
type ChatPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatPayloadValidationError) ErrorName() string { return "ChatPayloadValidationError" }

// Error satisfies the builtin error interface
func (e ChatPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatPayloadValidationError{}

// Validate checks the field values on CallAgentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CallAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CallAgentRequestMultiError, or nil if none found.
func (m *CallAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CallAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for AgentID

	// no validation rules for RoundID

	// no validation rules for ChatID

	// no validation rules for IsMultiRound

	// no validation rules for AiChatItemID

	// no validation rules for InternetSearch

	// no validation rules for Thinking

	if len(errors) > 0 {
		return CallAgentRequestMultiError(errors)
	}

	return nil
}

// CallAgentRequestMultiError is an error wrapping multiple validation errors
// returned by CallAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type CallAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallAgentRequestMultiError) AllErrors() []error { return m }

// CallAgentRequestValidationError is the validation error returned by
// CallAgentRequest.Validate if the designated constraints aren't met.
type CallAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallAgentRequestValidationError) ErrorName() string { return "CallAgentRequestValidationError" }

// Error satisfies the builtin error interface
func (e CallAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallAgentRequestValidationError{}

// Validate checks the field values on Usage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Usage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Usage with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in UsageMultiError, or nil if none found.
func (m *Usage) ValidateAll() error {
	return m.validate(true)
}

func (m *Usage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PromptTokens

	// no validation rules for CompletionTokens

	// no validation rules for TotalTokens

	if len(errors) > 0 {
		return UsageMultiError(errors)
	}

	return nil
}

// UsageMultiError is an error wrapping multiple validation errors returned by
// Usage.ValidateAll() if the designated constraints aren't met.
type UsageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UsageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UsageMultiError) AllErrors() []error { return m }

// UsageValidationError is the validation error returned by Usage.Validate if
// the designated constraints aren't met.
type UsageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UsageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UsageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UsageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UsageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UsageValidationError) ErrorName() string { return "UsageValidationError" }

// Error satisfies the builtin error interface
func (e UsageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUsage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UsageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UsageValidationError{}

// Validate checks the field values on CallAgentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallAgentReplyMultiError,
// or nil if none found.
func (m *CallAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CallAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RoundID

	// no validation rules for Content

	// no validation rules for Status

	for idx, item := range m.GetPayloads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallAgentReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallAgentReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallAgentReplyValidationError{
					field:  fmt.Sprintf("Payloads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Type

	// no validation rules for Reason

	// no validation rules for DebugContent

	if all {
		switch v := interface{}(m.GetUsage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CallAgentReplyValidationError{
					field:  "Usage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CallAgentReplyValidationError{
					field:  "Usage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUsage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CallAgentReplyValidationError{
				field:  "Usage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CachePayloads

	// no validation rules for IsCached

	if len(errors) > 0 {
		return CallAgentReplyMultiError(errors)
	}

	return nil
}

// CallAgentReplyMultiError is an error wrapping multiple validation errors
// returned by CallAgentReply.ValidateAll() if the designated constraints
// aren't met.
type CallAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallAgentReplyMultiError) AllErrors() []error { return m }

// CallAgentReplyValidationError is the validation error returned by
// CallAgentReply.Validate if the designated constraints aren't met.
type CallAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallAgentReplyValidationError) ErrorName() string { return "CallAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e CallAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallAgentReplyValidationError{}

// Validate checks the field values on CallAgentTestRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CallAgentTestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallAgentTestRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CallAgentTestRequestMultiError, or nil if none found.
func (m *CallAgentTestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CallAgentTestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for AgentID

	// no validation rules for RoundID

	if len(errors) > 0 {
		return CallAgentTestRequestMultiError(errors)
	}

	return nil
}

// CallAgentTestRequestMultiError is an error wrapping multiple validation
// errors returned by CallAgentTestRequest.ValidateAll() if the designated
// constraints aren't met.
type CallAgentTestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallAgentTestRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallAgentTestRequestMultiError) AllErrors() []error { return m }

// CallAgentTestRequestValidationError is the validation error returned by
// CallAgentTestRequest.Validate if the designated constraints aren't met.
type CallAgentTestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallAgentTestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallAgentTestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallAgentTestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallAgentTestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallAgentTestRequestValidationError) ErrorName() string {
	return "CallAgentTestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CallAgentTestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallAgentTestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallAgentTestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallAgentTestRequestValidationError{}

// Validate checks the field values on CallAgentTestReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CallAgentTestReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallAgentTestReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CallAgentTestReplyMultiError, or nil if none found.
func (m *CallAgentTestReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CallAgentTestReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	for idx, item := range m.GetPayloads() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CallAgentTestReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CallAgentTestReplyValidationError{
						field:  fmt.Sprintf("Payloads[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CallAgentTestReplyValidationError{
					field:  fmt.Sprintf("Payloads[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CallAgentTestReplyMultiError(errors)
	}

	return nil
}

// CallAgentTestReplyMultiError is an error wrapping multiple validation errors
// returned by CallAgentTestReply.ValidateAll() if the designated constraints
// aren't met.
type CallAgentTestReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallAgentTestReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallAgentTestReplyMultiError) AllErrors() []error { return m }

// CallAgentTestReplyValidationError is the validation error returned by
// CallAgentTestReply.Validate if the designated constraints aren't met.
type CallAgentTestReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallAgentTestReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallAgentTestReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallAgentTestReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallAgentTestReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallAgentTestReplyValidationError) ErrorName() string {
	return "CallAgentTestReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CallAgentTestReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallAgentTestReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallAgentTestReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallAgentTestReplyValidationError{}

// Validate checks the field values on CustomRule with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CustomRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomRule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomRuleMultiError, or
// nil if none found.
func (m *CustomRule) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleTitle

	// no validation rules for RuleContent

	if len(errors) > 0 {
		return CustomRuleMultiError(errors)
	}

	return nil
}

// CustomRuleMultiError is an error wrapping multiple validation errors
// returned by CustomRule.ValidateAll() if the designated constraints aren't met.
type CustomRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomRuleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomRuleMultiError) AllErrors() []error { return m }

// CustomRuleValidationError is the validation error returned by
// CustomRule.Validate if the designated constraints aren't met.
type CustomRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomRuleValidationError) ErrorName() string { return "CustomRuleValidationError" }

// Error satisfies the builtin error interface
func (e CustomRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomRuleValidationError{}

// Validate checks the field values on ContractReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContractReviewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContractReviewRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContractReviewRequestMultiError, or nil if none found.
func (m *ContractReviewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ContractReviewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCustomRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ContractReviewRequestValidationError{
						field:  fmt.Sprintf("CustomRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ContractReviewRequestValidationError{
						field:  fmt.Sprintf("CustomRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ContractReviewRequestValidationError{
					field:  fmt.Sprintf("CustomRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PartyA

	// no validation rules for FileContent

	if len(errors) > 0 {
		return ContractReviewRequestMultiError(errors)
	}

	return nil
}

// ContractReviewRequestMultiError is an error wrapping multiple validation
// errors returned by ContractReviewRequest.ValidateAll() if the designated
// constraints aren't met.
type ContractReviewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContractReviewRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContractReviewRequestMultiError) AllErrors() []error { return m }

// ContractReviewRequestValidationError is the validation error returned by
// ContractReviewRequest.Validate if the designated constraints aren't met.
type ContractReviewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContractReviewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContractReviewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContractReviewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContractReviewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContractReviewRequestValidationError) ErrorName() string {
	return "ContractReviewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ContractReviewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContractReviewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContractReviewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContractReviewRequestValidationError{}

// Validate checks the field values on ContractReviewReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContractReviewReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContractReviewReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContractReviewReplyMultiError, or nil if none found.
func (m *ContractReviewReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ContractReviewReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	if len(errors) > 0 {
		return ContractReviewReplyMultiError(errors)
	}

	return nil
}

// ContractReviewReplyMultiError is an error wrapping multiple validation
// errors returned by ContractReviewReply.ValidateAll() if the designated
// constraints aren't met.
type ContractReviewReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContractReviewReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContractReviewReplyMultiError) AllErrors() []error { return m }

// ContractReviewReplyValidationError is the validation error returned by
// ContractReviewReply.Validate if the designated constraints aren't met.
type ContractReviewReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContractReviewReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContractReviewReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContractReviewReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContractReviewReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContractReviewReplyValidationError) ErrorName() string {
	return "ContractReviewReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ContractReviewReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContractReviewReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContractReviewReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContractReviewReplyValidationError{}

// Validate checks the field values on QuestionSecurityCheckRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionSecurityCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionSecurityCheckRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionSecurityCheckRequestMultiError, or nil if none found.
func (m *QuestionSecurityCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionSecurityCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	if len(errors) > 0 {
		return QuestionSecurityCheckRequestMultiError(errors)
	}

	return nil
}

// QuestionSecurityCheckRequestMultiError is an error wrapping multiple
// validation errors returned by QuestionSecurityCheckRequest.ValidateAll() if
// the designated constraints aren't met.
type QuestionSecurityCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionSecurityCheckRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionSecurityCheckRequestMultiError) AllErrors() []error { return m }

// QuestionSecurityCheckRequestValidationError is the validation error returned
// by QuestionSecurityCheckRequest.Validate if the designated constraints
// aren't met.
type QuestionSecurityCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionSecurityCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionSecurityCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionSecurityCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionSecurityCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionSecurityCheckRequestValidationError) ErrorName() string {
	return "QuestionSecurityCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionSecurityCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionSecurityCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionSecurityCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionSecurityCheckRequestValidationError{}

// Validate checks the field values on QuestionSecurityCheckReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuestionSecurityCheckReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuestionSecurityCheckReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuestionSecurityCheckReplyMultiError, or nil if none found.
func (m *QuestionSecurityCheckReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QuestionSecurityCheckReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Hit

	if len(errors) > 0 {
		return QuestionSecurityCheckReplyMultiError(errors)
	}

	return nil
}

// QuestionSecurityCheckReplyMultiError is an error wrapping multiple
// validation errors returned by QuestionSecurityCheckReply.ValidateAll() if
// the designated constraints aren't met.
type QuestionSecurityCheckReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuestionSecurityCheckReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuestionSecurityCheckReplyMultiError) AllErrors() []error { return m }

// QuestionSecurityCheckReplyValidationError is the validation error returned
// by QuestionSecurityCheckReply.Validate if the designated constraints aren't met.
type QuestionSecurityCheckReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuestionSecurityCheckReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuestionSecurityCheckReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuestionSecurityCheckReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuestionSecurityCheckReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuestionSecurityCheckReplyValidationError) ErrorName() string {
	return "QuestionSecurityCheckReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QuestionSecurityCheckReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuestionSecurityCheckReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuestionSecurityCheckReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuestionSecurityCheckReplyValidationError{}

// Validate checks the field values on CheckAndSaveQuestionSemanticCacheRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckAndSaveQuestionSemanticCacheRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckAndSaveQuestionSemanticCacheRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CheckAndSaveQuestionSemanticCacheRequestMultiError, or nil if none found.
func (m *CheckAndSaveQuestionSemanticCacheRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckAndSaveQuestionSemanticCacheRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for RefFiles

	// no validation rules for AgentID

	if len(errors) > 0 {
		return CheckAndSaveQuestionSemanticCacheRequestMultiError(errors)
	}

	return nil
}

// CheckAndSaveQuestionSemanticCacheRequestMultiError is an error wrapping
// multiple validation errors returned by
// CheckAndSaveQuestionSemanticCacheRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckAndSaveQuestionSemanticCacheRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckAndSaveQuestionSemanticCacheRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckAndSaveQuestionSemanticCacheRequestMultiError) AllErrors() []error { return m }

// CheckAndSaveQuestionSemanticCacheRequestValidationError is the validation
// error returned by CheckAndSaveQuestionSemanticCacheRequest.Validate if the
// designated constraints aren't met.
type CheckAndSaveQuestionSemanticCacheRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) ErrorName() string {
	return "CheckAndSaveQuestionSemanticCacheRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckAndSaveQuestionSemanticCacheRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckAndSaveQuestionSemanticCacheRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckAndSaveQuestionSemanticCacheRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckAndSaveQuestionSemanticCacheRequestValidationError{}

// Validate checks the field values on CheckAndSaveQuestionSemanticCacheReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckAndSaveQuestionSemanticCacheReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CheckAndSaveQuestionSemanticCacheReply with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CheckAndSaveQuestionSemanticCacheReplyMultiError, or nil if none found.
func (m *CheckAndSaveQuestionSemanticCacheReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckAndSaveQuestionSemanticCacheReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CheckAndSaveQuestionSemanticCacheReplyMultiError(errors)
	}

	return nil
}

// CheckAndSaveQuestionSemanticCacheReplyMultiError is an error wrapping
// multiple validation errors returned by
// CheckAndSaveQuestionSemanticCacheReply.ValidateAll() if the designated
// constraints aren't met.
type CheckAndSaveQuestionSemanticCacheReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckAndSaveQuestionSemanticCacheReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckAndSaveQuestionSemanticCacheReplyMultiError) AllErrors() []error { return m }

// CheckAndSaveQuestionSemanticCacheReplyValidationError is the validation
// error returned by CheckAndSaveQuestionSemanticCacheReply.Validate if the
// designated constraints aren't met.
type CheckAndSaveQuestionSemanticCacheReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) ErrorName() string {
	return "CheckAndSaveQuestionSemanticCacheReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CheckAndSaveQuestionSemanticCacheReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckAndSaveQuestionSemanticCacheReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckAndSaveQuestionSemanticCacheReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckAndSaveQuestionSemanticCacheReplyValidationError{}
