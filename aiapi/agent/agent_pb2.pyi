from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ChatPayload(_message.Message):
    __slots__ = ("fileRelationID", "entityTag", "preEntityTag", "name", "size", "userID", "mimeType", "index", "chunkIndex", "chunkSize", "content", "imageKeys", "fullPath")
    FILERELATIONID_FIELD_NUMBER: _ClassVar[int]
    ENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    PREENTITYTAG_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SIZE_FIELD_NUMBER: _ClassVar[int]
    USERID_FIELD_NUMBER: _ClassVar[int]
    MIMETYPE_FIELD_NUMBER: _ClassVar[int]
    INDEX_FIELD_NUMBER: _ClassVar[int]
    CHUNKINDEX_FIELD_NUMBER: _ClassVar[int]
    CHUNKSIZE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    IMAGEKEYS_FIELD_NUMBER: _ClassVar[int]
    FULLPATH_FIELD_NUMBER: _ClassVar[int]
    fileRelationID: int
    entityTag: str
    preEntityTag: str
    name: str
    size: int
    userID: int
    mimeType: str
    index: int
    chunkIndex: int
    chunkSize: int
    content: str
    imageKeys: _containers.RepeatedScalarFieldContainer[str]
    fullPath: str
    def __init__(self, fileRelationID: _Optional[int] = ..., entityTag: _Optional[str] = ..., preEntityTag: _Optional[str] = ..., name: _Optional[str] = ..., size: _Optional[int] = ..., userID: _Optional[int] = ..., mimeType: _Optional[str] = ..., index: _Optional[int] = ..., chunkIndex: _Optional[int] = ..., chunkSize: _Optional[int] = ..., content: _Optional[str] = ..., imageKeys: _Optional[_Iterable[str]] = ..., fullPath: _Optional[str] = ...) -> None: ...

class CallAgentRequest(_message.Message):
    __slots__ = ("query", "agentID", "roundID", "fileRelationIDs", "chatID", "isMultiRound", "aiChatItemID", "internetSearch", "thinking")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    AGENTID_FIELD_NUMBER: _ClassVar[int]
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    FILERELATIONIDS_FIELD_NUMBER: _ClassVar[int]
    CHATID_FIELD_NUMBER: _ClassVar[int]
    ISMULTIROUND_FIELD_NUMBER: _ClassVar[int]
    AICHATITEMID_FIELD_NUMBER: _ClassVar[int]
    INTERNETSEARCH_FIELD_NUMBER: _ClassVar[int]
    THINKING_FIELD_NUMBER: _ClassVar[int]
    query: str
    agentID: int
    roundID: int
    fileRelationIDs: _containers.RepeatedScalarFieldContainer[int]
    chatID: int
    isMultiRound: bool
    aiChatItemID: int
    internetSearch: bool
    thinking: bool
    def __init__(self, query: _Optional[str] = ..., agentID: _Optional[int] = ..., roundID: _Optional[int] = ..., fileRelationIDs: _Optional[_Iterable[int]] = ..., chatID: _Optional[int] = ..., isMultiRound: bool = ..., aiChatItemID: _Optional[int] = ..., internetSearch: bool = ..., thinking: bool = ...) -> None: ...

class Usage(_message.Message):
    __slots__ = ("promptTokens", "completionTokens", "totalTokens")
    PROMPTTOKENS_FIELD_NUMBER: _ClassVar[int]
    COMPLETIONTOKENS_FIELD_NUMBER: _ClassVar[int]
    TOTALTOKENS_FIELD_NUMBER: _ClassVar[int]
    promptTokens: int
    completionTokens: int
    totalTokens: int
    def __init__(self, promptTokens: _Optional[int] = ..., completionTokens: _Optional[int] = ..., totalTokens: _Optional[int] = ...) -> None: ...

class CallAgentReply(_message.Message):
    __slots__ = ("roundID", "content", "status", "payloads", "type", "reason", "debugContent", "usage", "cachePayloads", "isCached")
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PAYLOADS_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    REASON_FIELD_NUMBER: _ClassVar[int]
    DEBUGCONTENT_FIELD_NUMBER: _ClassVar[int]
    USAGE_FIELD_NUMBER: _ClassVar[int]
    CACHEPAYLOADS_FIELD_NUMBER: _ClassVar[int]
    ISCACHED_FIELD_NUMBER: _ClassVar[int]
    roundID: int
    content: str
    status: int
    payloads: _containers.RepeatedCompositeFieldContainer[ChatPayload]
    type: int
    reason: str
    debugContent: str
    usage: Usage
    cachePayloads: str
    isCached: bool
    def __init__(self, roundID: _Optional[int] = ..., content: _Optional[str] = ..., status: _Optional[int] = ..., payloads: _Optional[_Iterable[_Union[ChatPayload, _Mapping]]] = ..., type: _Optional[int] = ..., reason: _Optional[str] = ..., debugContent: _Optional[str] = ..., usage: _Optional[_Union[Usage, _Mapping]] = ..., cachePayloads: _Optional[str] = ..., isCached: bool = ...) -> None: ...

class CallAgentTestRequest(_message.Message):
    __slots__ = ("query", "agentID", "roundID")
    QUERY_FIELD_NUMBER: _ClassVar[int]
    AGENTID_FIELD_NUMBER: _ClassVar[int]
    ROUNDID_FIELD_NUMBER: _ClassVar[int]
    query: str
    agentID: int
    roundID: int
    def __init__(self, query: _Optional[str] = ..., agentID: _Optional[int] = ..., roundID: _Optional[int] = ...) -> None: ...

class CallAgentTestReply(_message.Message):
    __slots__ = ("content", "payloads")
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    PAYLOADS_FIELD_NUMBER: _ClassVar[int]
    content: str
    payloads: _containers.RepeatedCompositeFieldContainer[ChatPayload]
    def __init__(self, content: _Optional[str] = ..., payloads: _Optional[_Iterable[_Union[ChatPayload, _Mapping]]] = ...) -> None: ...

class CustomRule(_message.Message):
    __slots__ = ("ruleTitle", "ruleContent")
    RULETITLE_FIELD_NUMBER: _ClassVar[int]
    RULECONTENT_FIELD_NUMBER: _ClassVar[int]
    ruleTitle: str
    ruleContent: str
    def __init__(self, ruleTitle: _Optional[str] = ..., ruleContent: _Optional[str] = ...) -> None: ...

class ContractReviewRequest(_message.Message):
    __slots__ = ("customRules", "partyA", "fileContent")
    CUSTOMRULES_FIELD_NUMBER: _ClassVar[int]
    PARTYA_FIELD_NUMBER: _ClassVar[int]
    FILECONTENT_FIELD_NUMBER: _ClassVar[int]
    customRules: _containers.RepeatedCompositeFieldContainer[CustomRule]
    partyA: bool
    fileContent: str
    def __init__(self, customRules: _Optional[_Iterable[_Union[CustomRule, _Mapping]]] = ..., partyA: bool = ..., fileContent: _Optional[str] = ...) -> None: ...

class ContractReviewReply(_message.Message):
    __slots__ = ("content",)
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    content: str
    def __init__(self, content: _Optional[str] = ...) -> None: ...

class QuestionSecurityCheckRequest(_message.Message):
    __slots__ = ("question", "policies")
    QUESTION_FIELD_NUMBER: _ClassVar[int]
    POLICIES_FIELD_NUMBER: _ClassVar[int]
    question: str
    policies: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, question: _Optional[str] = ..., policies: _Optional[_Iterable[str]] = ...) -> None: ...

class QuestionSecurityCheckReply(_message.Message):
    __slots__ = ("hit",)
    HIT_FIELD_NUMBER: _ClassVar[int]
    hit: bool
    def __init__(self, hit: bool = ...) -> None: ...

class CheckAndSaveQuestionSemanticCacheRequest(_message.Message):
    __slots__ = ("question", "answer", "refFiles", "agentID")
    QUESTION_FIELD_NUMBER: _ClassVar[int]
    ANSWER_FIELD_NUMBER: _ClassVar[int]
    REFFILES_FIELD_NUMBER: _ClassVar[int]
    AGENTID_FIELD_NUMBER: _ClassVar[int]
    question: str
    answer: str
    refFiles: str
    agentID: int
    def __init__(self, question: _Optional[str] = ..., answer: _Optional[str] = ..., refFiles: _Optional[str] = ..., agentID: _Optional[int] = ...) -> None: ...

class CheckAndSaveQuestionSemanticCacheReply(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
