import requests
from config.config import config
from datahelper.logger import get_logger

logger = get_logger(__name__)

class Store:
    def __init__(self):
       self.address = config.get_naming_instance("store.http")

    def UpdateFileQARedisStatus(self, file_relation_id):
        if self.address == "":
            logger.error("failed to get data-ingestion host")
        try:
            resp = requests.post("http://{}/store/file/updateRedisStatus".format(self.address),
                                 json = {"fileRelationID": file_relation_id, "qa": True})
            if resp.status_code != 200:
                logger.error("UpdateFileQARedisStatus error, resp: {}".format(resp.text))
        except Exception as e:
            logger.error("UpdateFileQARedisStatus error, error: {}".format(e))
