import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiAgentSecurityLog(Base):
    __tablename__ = 'ai_agent_security_log'
    id = Column(BigInteger, primary_key=True)
    risk_level = Column(BigInteger, nullable=False)
    user_id = Column(BigInteger, nullable=False)
    user_name = Column(String(255), nullable=False)
    pc_name = Column(String(255), nullable=False)
    agent_id = Column(BigInteger, nullable=False)
    agent_name = Column(String(255), nullable=False)
    agent_description = Column(String(255), nullable=False)
    action_category = Column(BigInteger, nullable=False)
    hit_action = Column(BigInteger, nullable=False)
    question = Column(Text, nullable=False)
    uploaded_files = Column(ARRAY(Text), nullable=False)
    hit_policies = Column(ARRAY(Text), nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False)
    deleted_at = Column(TIMESTAMP(timezone=True), nullable=True)
    
    @staticmethod
    def create_log(
        risk_level: int,
        user_id: int,
        user_name: str,
        pc_name: str,
        agent_id: int,
        agent_name: str,
        agent_description: str,
        action_category: int,
        hit_action: int,
        question: str,
        uploaded_files: list,
        hit_policies: list
    ) -> 'AiAgentSecurityLog':
        """
        创建新的安全日志记录
        
        Args:
            risk_level: 风险等级
            user_id: 用户ID
            user_name: 用户名
            pc_name: PC名称
            agent_id: 代理ID
            agent_name: 代理名称
            agent_description: 代理描述
            action_category: 动作类别
            hit_action: 命中动作
            question: 问题内容
            uploaded_files: 上传的文件列表
            hit_policies: 命中的策略列表
            
        Returns:
            AiAgentSecurityLog: 创建的安全日志对象
        """
        log = AiAgentSecurityLog(
            risk_level=risk_level,
            user_id=user_id,
            user_name=user_name,
            pc_name=pc_name,
            agent_id=agent_id,
            agent_name=agent_name,
            agent_description=agent_description,
            action_category=action_category,
            hit_action=hit_action,
            question=question,
            uploaded_files=uploaded_files,
            hit_policies=hit_policies,
            created_at=datetime.datetime.now(datetime.timezone.utc),
            updated_at=datetime.datetime.now(datetime.timezone.utc),
            deleted_at=datetime.datetime(1, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)
        )
        
        with session_scope() as session:
            session.add(log)
            session.flush()  # 获取自动生成的ID
            return log

