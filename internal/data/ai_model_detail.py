import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiModelDetail(Base):
    __tablename__ = 'ai_model_detail'
    id = Column(BigInteger, primary_key=True)
    name = Column(String(255), nullable=False)
    model_name = Column(String(255), nullable=True)
    url = Column(String(255), nullable=True)

    @classmethod
    def get_ai_model_detail_by_id(cls, id):
        with session_scope() as session:
            return session.query(cls).filter(cls.id == id).first()

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}
