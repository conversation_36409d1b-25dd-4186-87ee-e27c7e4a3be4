import unittest
from internal.data.chat_history import ChatHistory


class TestChatHistory(unittest.TestCase):
    def test_add_record(self):
        ch = ChatHistory()
        ch.add_chat_record(1, 1, 1, "test", "test", 10)
        ch.add_chat_record(1, 1, 1, "test2", "test2", 10)

    def test_get_record(self):
        ch = ChatHistory()
        print(ch.get_chat_history_by_round_id(1, 1, 1))
        res = ch.get_chat_history(1, 1, 10, 0)
        print(res)
