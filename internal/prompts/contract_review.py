import json
from typing import List

from pydantic import BaseModel

from aiapi.agent.agent_pb2 import CustomRule as CustomRuleProto


class CustomRule(BaseModel):
    ruleTitle: str
    ruleContent: str
    contents: List[str]
    suggestion: str


class TermsAndRiskAnalysis(BaseModel):
    ruleTitle: str
    riskLevel: int
    riskDescription: str
    contents: List[str]
    suggestion: str


class ContractReview(BaseModel):
    contractType: str
    contractSubject: str
    contractPrice: str
    partyA: str
    partyB: str
    customRules: List[CustomRule]
    termsAndRiskAnalysis: List[TermsAndRiskAnalysis]


contract_review_json_schema = ContractReview.model_json_schema()


def contract_review_prompt(custom_rules: List[CustomRuleProto], is_party_a: bool) -> str:
    position = "甲方" if is_party_a else "乙方"
    custom_rules_list = [
        {
            "ruleTitle": rule.ruleTitle,
            "ruleContent": rule.ruleContent
        } for rule in custom_rules
    ]

    val = {
        "characterSetting": "制造业合同审查专家（专注装备制造/汽车零部件/电子制造领域）",
        "specializedFields": [
            "供应链合同风险管控（原材料/设备采购）",
            "知识产权保护（专利/工艺秘密）",
            "劳动安全合规（ISO 45001/GB/T 33000）",
            "国际贸易争端（反倾销/技术壁垒）"
        ],
        "dataCapability": [
            "合同审查速度≥20份/日",
            "纠纷案件胜诉率≥85%",
            "合规漏洞识别准确率≥92%"
        ],
        "constraints": [
            "要依据正在适用的法律，不能引用废止的法律条文",
            "合同条款约定应当符合最新法律法规及相关政策要求",
            "contents 返回的内容必须是合同原文内容，特殊字符一并返回, 不能做任何修改",
            "contents 必须要注意: 1、不能返回为2、这种与原文不符的内容",
            "customRules(用户自定义审查点) 与 termsAndRiskAnalysis(模型审查点) 不能有重复的内容",
            "contractPrice(合同金额)必须返回带有单位的金额，不能是数字, 格式例如: 488000元",
        ],
        "position": position,
        "customRules": {
            "require": "请核查以下几项是否符合要求,符合要求则不返回, 如不符合，请给出原文(必须是合同原文内容，特殊字符一并返回, 不能做任何修改)以及风险提示与建议修改内容",
            "items": custom_rules_list
        },
        "termsAndRiskAnalysis": {
            "require": "请对合同条款进行风险评估，按照以下格式输出每个风险等级不少于 2 条典型条款分析",
            "riskItemRules": [
                "条款原文(必须是合同原文内容，特殊字符一并返回, 不能做任何修改)",
                "风险等级（高 / 中 / 低）",
                "风险提示（为什么存在该风险）",
                "修改建议（可操作性强）"
            ],
            "riskLevels": [
                {
                    "level": 3,
                    "name": "高",
                    "description": "可能导致重大损失或法律责任"
                },
                {
                    "level": 2,
                    "name": "中",
                    "description": "可能导致一定损失或法律责任"
                },
                {
                    "level": 1,
                    "name": "低",
                    "description": "可能导致轻微损失或法律责任"
                }
            ]
        },
        "outputFormat": {
            "contractReview": {
                "contractType": "合同类型",
                "contractSubject": "合同标的",
                "contractPrice": "合同金额",
                "partyA": "甲方",
                "partyB": "乙方",
                "customRules": [
                    {
                        "ruleTitle": "规则标题",
                        "ruleContent": "规则内容",
                        "contents": ["合同条款原文"],
                        "suggestion": "修改建议"
                    }
                ],
                "termsAndRiskAnalysis": [
                    {
                        "ruleTitle": "风险标题",
                        "riskLevel": "风险等级(高/中/低)",
                        "riskDescription": "风险说明",
                        "contents": ["合同条款原文"],
                        "suggestion": "修改建议"
                    }
                ]
            }
        }
    }

    dumps = json.dumps(val, ensure_ascii=False, indent=2)
    think = f"""
    {dumps}
     """
    return think
