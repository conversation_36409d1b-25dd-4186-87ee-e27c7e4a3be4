syntax = "proto3";

package aiapi.agent;

option go_package = "gitlab.minum.cloud/innovationteam/ai-api/aiapi/agent;agent";

message ChatPayload {
  int64 fileRelationID = 1;
  string entityTag = 2;
  string preEntityTag = 3;
  string name = 4;
  int64 size = 5;
  int64 userID = 6;
  string mimeType = 7;
  int64 index = 8;
  int64 chunkIndex = 9;
  int64 chunkSize = 10;
  string content = 11;
  repeated string imageKeys = 12;
  string fullPath = 13;
}

message CallAgentRequest {
  string query = 1;
  int64 agentID = 2;
  int64 roundID = 3;
  repeated int64 fileRelationIDs = 4;
  int64 chatID = 5;
  bool isMultiRound = 6;
  int64 aiChatItemID = 7;
  bool internetSearch = 8;
  bool thinking = 9;
}

message Usage {
  int64 promptTokens = 1;
  int64 completionTokens = 2;
  int64 totalTokens = 3;
}

message CallAgentReply {
  int64 roundID = 1;
  string content = 2;
  int64 status = 3;
  repeated ChatPayload payloads = 4;
  int64 type = 5;
  string reason = 6;
  string debugContent = 7;
  Usage usage = 8;
  string cachePayloads = 9;
  bool isCached = 10;
}

message CallAgentTestRequest {
  string query = 1;
  int64 agentID = 2;
  int64 roundID = 3;
}

message CallAgentTestReply {
  string content = 1;
  repeated ChatPayload payloads = 2;
}

message CustomRule {
  string ruleTitle = 1;
  string ruleContent = 2;
}

message ContractReviewRequest {
  repeated CustomRule customRules = 1;
  bool partyA = 2;
  string fileContent = 3;
}

message ContractReviewReply {
  string content = 1;
}

message QuestionSecurityCheckRequest {
  string question = 1;
  repeated string policies = 2;
}

message QuestionSecurityCheckReply {
  bool hit = 1;
}

message CheckAndSaveQuestionSemanticCacheRequest {
  string question = 1;
  string answer = 2;
  string refFiles = 3;
  int64 agentID = 4;
}

message CheckAndSaveQuestionSemanticCacheReply {}

service Agent {
  rpc CallAgent(CallAgentRequest) returns (stream CallAgentReply);

  rpc CallAgentTest(CallAgentTestRequest) returns (CallAgentTestReply);

  rpc ContractReview(ContractReviewRequest) returns (ContractReviewReply);

  rpc QuestionSecurityCheck(QuestionSecurityCheckRequest) returns (QuestionSecurityCheckReply);

  rpc CheckAndSaveQuestionSemanticCache(CheckAndSaveQuestionSemanticCacheRequest) returns (CheckAndSaveQuestionSemanticCacheReply);
}
