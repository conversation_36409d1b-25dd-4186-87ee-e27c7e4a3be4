def classify_message_prompt(message: str) -> str:
    """
    创建一个提示模板，用于分类消息
    一级分类：工作、私人
    工作分类下的二级分类：问题解决、信息获取、决策辅助、创新发展、其他
    
    :param message: 需要分类的消息内容
    :return: 用于大模型的提示模板
    """
    return f"""
    你是一位专业的消息分类专家，需要对给定的消息进行精确分类。

---Goal---

对提供的消息内容进行一级分类和二级分类（如适用）。

---Instructions---

• 一级分类包括：
  - 工作：与工作相关的消息
  - 私人：与个人生活相关的消息

• 工作类别下的二级分类包括：
  - 问题解决：请求解决特定工作问题或技术困难
  - 信息获取：寻求获取工作相关信息或数据
  - 决策辅助：需要帮助做出工作决策或判断
  - 创新发展：关于新想法、创新方案或业务发展
  - 其他：不属于上述类别的工作相关消息

• 私人类别下的二级分类包括：
  - 私人：与个人生活相关的消息

• 输出格式必须为JSON，含有以下字段：
  - primary_category: 一级分类，必须是"工作"或"私人"之一
  - secondary_category: 二级分类，当primary_category为"工作"时，必须是"问题解决"、"信息获取"、"决策辅助"、"创新发展"或"其他"之一；当primary_category为"私人"时，值为"私人"

---Examples---

Example 1:
消息：我们需要解决服务器崩溃问题，用户无法访问我们的网站，请提供可能的解决方案。
Output:
{{
    "primary_category": "工作",
    "secondary_category": "问题解决"
}}

Example 2:
消息：你能帮我找一下上个季度的销售数据报告吗？我需要准备明天的会议。
Output:
{{
    "primary_category": "工作",
    "secondary_category": "信息获取"
}}

Example 3:
消息：我儿子明天的生日派对要准备什么礼物比较好？他今年10岁。
Output:
{{
    "primary_category": "私人",
    "secondary_category": "私人"
}}

Example 4:
消息：你认为我们应该采用新的市场策略还是继续沿用现有策略？现有数据显示增长放缓。
Output:
{{
    "primary_category": "工作",
    "secondary_category": "决策辅助"
}}

Example 5:
消息：我有一个新的产品创意，可以解决用户在移动端体验的痛点，想听听你的看法。
Output:
{{
    "primary_category": "工作",
    "secondary_category": "创新发展"
}}

---Real Data---

消息：
{message}

######################
Output:
    """

def extract_classification_from_response(response: str) -> dict:
    """
    从大模型返回的响应中提取分类结果
    
    :param response: 大模型的原始响应
    :return: 包含分类信息的字典
    """
    import json
    import re
    
    # 尝试找到JSON格式的内容
    try:
        # 清理响应中的非JSON内容
        json_match = re.search(r'(\{.*\})', response, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            # 解析JSON
            result = json.loads(json_str)
            
            # 验证返回的字段
            required_fields = ['primary_category', 'secondary_category']
            if all(field in result for field in required_fields):
                return result
            else:
                raise ValueError("返回的JSON缺少必要字段")
        else:
            raise ValueError("无法从响应中提取JSON内容")
    except Exception as e:
        # 如果解析失败，返回默认分类
        return {
            "primary_category": "工作",
            "secondary_category": "其他"
        } 