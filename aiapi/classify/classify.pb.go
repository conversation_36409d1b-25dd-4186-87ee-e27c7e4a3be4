// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: classify/classify.proto

package classify

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClassifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityTag          string   `protobuf:"bytes,1,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag       string   `protobuf:"bytes,2,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	FileRelationID     int64    `protobuf:"varint,3,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	ExistingCategories []string `protobuf:"bytes,4,rep,name=existing_categories,json=existingCategories,proto3" json:"existing_categories,omitempty"`
}

func (x *ClassifyRequest) Reset() {
	*x = ClassifyRequest{}
	mi := &file_classify_classify_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClassifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassifyRequest) ProtoMessage() {}

func (x *ClassifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_classify_classify_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassifyRequest.ProtoReflect.Descriptor instead.
func (*ClassifyRequest) Descriptor() ([]byte, []int) {
	return file_classify_classify_proto_rawDescGZIP(), []int{0}
}

func (x *ClassifyRequest) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ClassifyRequest) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ClassifyRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ClassifyRequest) GetExistingCategories() []string {
	if x != nil {
		return x.ExistingCategories
	}
	return nil
}

type ClassifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuggestedCategory string `protobuf:"bytes,1,opt,name=suggestedCategory,proto3" json:"suggestedCategory,omitempty"`
}

func (x *ClassifyResponse) Reset() {
	*x = ClassifyResponse{}
	mi := &file_classify_classify_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClassifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassifyResponse) ProtoMessage() {}

func (x *ClassifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_classify_classify_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassifyResponse.ProtoReflect.Descriptor instead.
func (*ClassifyResponse) Descriptor() ([]byte, []int) {
	return file_classify_classify_proto_rawDescGZIP(), []int{1}
}

func (x *ClassifyResponse) GetSuggestedCategory() string {
	if x != nil {
		return x.SuggestedCategory
	}
	return ""
}

var File_classify_classify_proto protoreflect.FileDescriptor

var file_classify_classify_proto_rawDesc = []byte{
	0x0a, 0x17, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x61, 0x69, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x22, 0xac, 0x01, 0x0a, 0x0f, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70,
	0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12,
	0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x2f, 0x0a, 0x13, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x40, 0x0a, 0x10, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x32, 0x62, 0x0a, 0x0f, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4f, 0x0a,
	0x0a, 0x44, 0x6f, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x12, 0x1f, 0x2e, 0x61, 0x69,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x2e, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61,
	0x69, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x2e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x42,
	0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74,
	0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x79, 0x3b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_classify_classify_proto_rawDescOnce sync.Once
	file_classify_classify_proto_rawDescData = file_classify_classify_proto_rawDesc
)

func file_classify_classify_proto_rawDescGZIP() []byte {
	file_classify_classify_proto_rawDescOnce.Do(func() {
		file_classify_classify_proto_rawDescData = protoimpl.X.CompressGZIP(file_classify_classify_proto_rawDescData)
	})
	return file_classify_classify_proto_rawDescData
}

var file_classify_classify_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_classify_classify_proto_goTypes = []any{
	(*ClassifyRequest)(nil),  // 0: aiapi.classify.ClassifyRequest
	(*ClassifyResponse)(nil), // 1: aiapi.classify.ClassifyResponse
}
var file_classify_classify_proto_depIdxs = []int32{
	0, // 0: aiapi.classify.ClassifyService.DoClassify:input_type -> aiapi.classify.ClassifyRequest
	1, // 1: aiapi.classify.ClassifyService.DoClassify:output_type -> aiapi.classify.ClassifyResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_classify_classify_proto_init() }
func file_classify_classify_proto_init() {
	if File_classify_classify_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_classify_classify_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_classify_classify_proto_goTypes,
		DependencyIndexes: file_classify_classify_proto_depIdxs,
		MessageInfos:      file_classify_classify_proto_msgTypes,
	}.Build()
	File_classify_classify_proto = out.File
	file_classify_classify_proto_rawDesc = nil
	file_classify_classify_proto_goTypes = nil
	file_classify_classify_proto_depIdxs = nil
}
