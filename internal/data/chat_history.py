import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, TEXT, TIMESTAMP

from internal.data.db import session_scope, engine
from config.config import config

Base = declarative_base()


class ChatHistory(Base):
    __tablename__ = 'chat_history'
    id = Column(BigInteger, primary_key=True)
    user_id = Column(BigInteger)
    tenant_id = Column(BigInteger)
    round_id = Column(BigInteger)
    query = Column(TEXT)
    answer = Column(TEXT)
    payloads = Column(TEXT)
    created_at = Column(TIMESTAMP(timezone=True))
    tokens = Column(BigInteger)

    @classmethod
    def add_chat_record(cls, user_id, tenant_id, round_id, query, answer, tokens, payloads="") -> None:
        with session_scope() as session:
            session.add(cls(
                user_id=user_id,
                tenant_id=tenant_id,
                round_id=round_id,
                query=query,
                answer=answer,
                payloads=payloads,
                tokens=tokens,
                created_at=datetime.datetime.utcnow()
            ))

    @classmethod
    def get_chat_history_by_round_id(cls, user_id, tenant_id, round_id) -> list:
        with session_scope() as session:
            return session.query(cls).filter(cls.user_id == user_id, cls.tenant_id == tenant_id,
                                             cls.round_id == round_id).order_by(cls.id.desc()).\
                limit(config.llm_chat_history_poll_size_per_round).all()

    @classmethod
    def get_chat_history(cls, user_id, tenant_id, page_size, offset) -> list:
        with session_scope() as session:
            return session.query(cls).filter(cls.user_id == user_id, cls.tenant_id == tenant_id). \
                order_by(cls.id.desc()).offset(offset).limit(page_size).all()