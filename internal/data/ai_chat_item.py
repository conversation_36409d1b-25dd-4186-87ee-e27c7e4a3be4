import datetime

from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, BigInteger, String, TIMESTAMP, ARRAY, SmallInteger, Text, Boolean
from internal.data.db import session_scope

Base = declarative_base()

class AiChatItem(Base):
    __tablename__ = 'ai_chat_item'
    id = Column(BigInteger, primary_key=True)
    chat_id = Column(BigInteger, nullable=False)
    object_id = Column(BigInteger, nullable=False)
    object_type = Column(SmallInteger, nullable=False)
    message = Column(Text, nullable=False)
    agree_status = Column(SmallInteger, nullable=False)
    tenant_id = Column(BigInteger, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=True), nullable=False)
    ref_files = Column(ARRAY(Text), nullable=False)
    round_id = Column(BigInteger, nullable=False)
    primary_classification = Column(String(255), nullable=True)
    secondary_classification = Column(String(255), nullable=True)


    @classmethod
    def get_ai_chat_item_by_id(cls, id):
        with session_scope() as session:
            result = session.query(cls).filter(cls.id == id).first()
            if result is None:
                raise ValueError(f"AiChatItem with id {id} not found")
            return result
        
    @classmethod
    def get_ai_chat_items_by_chat_id(cls, chat_id):
        with session_scope() as session:
            result = session.query(cls).filter(cls.chat_id == chat_id).all()
            return result
        
    @classmethod
    def get_ai_chat_items_by_chat_id_last10(cls, chat_id):
        with session_scope() as session:
            result = session.query(cls).filter(cls.chat_id == chat_id).order_by(cls.created_at.desc()).limit(10).all()
            return result
        
    @classmethod
    def update_ai_chat_item_classification(cls, id, primary_classification, secondary_classification):
        with session_scope() as session:
            session.query(cls).filter(cls.id == id).update({
                cls.primary_classification: primary_classification,
                cls.secondary_classification: secondary_classification
            })
            session.commit()

    def to_dict(self):
        return {
            'id': self.id,
            'chat_id': self.chat_id,
            'object_id': self.object_id,
            'object_type': self.object_type,
            'message': self.message,
            'agree_status': self.agree_status,
            'tenant_id': self.tenant_id,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'ref_files': self.ref_files,
            'round_id': self.round_id,
            'primary_classification': self.primary_classification,
            'secondary_classification': self.secondary_classification
        }
        
    

